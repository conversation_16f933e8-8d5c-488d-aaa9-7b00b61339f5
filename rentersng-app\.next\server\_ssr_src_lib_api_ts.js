"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_api_ts";
exports.ids = ["_ssr_src_lib_api_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchProperties: () => (/* binding */ fetchProperties),\n/* harmony export */   fetchPropertyById: () => (/* binding */ fetchPropertyById),\n/* harmony export */   fetchRecentProperties: () => (/* binding */ fetchRecentProperties)\n/* harmony export */ });\n/**\n * API functions for fetching data from the server\n */ // Fetch properties with filtering\nasync function fetchProperties(params) {\n    try {\n        // Build query string from params\n        const queryParams = new URLSearchParams();\n        if (params.type) {\n            queryParams.append(\"listingType\", params.type.toUpperCase());\n        }\n        if (params.propertyType && params.propertyType !== \"all\") {\n            queryParams.append(\"propertyType\", params.propertyType.toUpperCase());\n        }\n        if (params.minPrice) {\n            queryParams.append(\"minPrice\", params.minPrice.toString());\n        }\n        if (params.maxPrice) {\n            queryParams.append(\"maxPrice\", params.maxPrice.toString());\n        }\n        if (params.bedrooms && params.bedrooms !== \"any\") {\n            queryParams.append(\"bedrooms\", params.bedrooms);\n        }\n        if (params.city && params.city !== \"all\") {\n            queryParams.append(\"city\", params.city);\n        }\n        if (params.state) {\n            queryParams.append(\"state\", params.state);\n        }\n        if (params.featured !== undefined) {\n            queryParams.append(\"featured\", params.featured.toString());\n        }\n        // Always filter for published properties\n        queryParams.append(\"published\", \"true\");\n        // Sorting\n        if (params.sortBy) {\n            queryParams.append(\"sortBy\", params.sortBy);\n        }\n        if (params.sortOrder) {\n            queryParams.append(\"sortOrder\", params.sortOrder);\n        }\n        // Pagination\n        if (params.limit) {\n            queryParams.append(\"take\", params.limit.toString());\n        }\n        if (params.page) {\n            const skip = (params.page - 1) * (params.limit || 10);\n            queryParams.append(\"skip\", skip.toString());\n        }\n        const response = await fetch(`/api/properties?${queryParams.toString()}`);\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch properties\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching properties:\", error);\n        return {\n            properties: [],\n            total: 0\n        };\n    }\n}\n// Fetch a single property by ID\nasync function fetchPropertyById(id) {\n    try {\n        const response = await fetch(`/api/properties/${id}`);\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch property\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(`Error fetching property ${id}:`, error);\n        return null;\n    }\n}\n// Fetch recently added properties\nasync function fetchRecentProperties(limit = 4) {\n    try {\n        const response = await fetch(`/api/properties/recent?limit=${limit}`);\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch recent properties\");\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching recent properties:\", error);\n        return [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ })

};
;