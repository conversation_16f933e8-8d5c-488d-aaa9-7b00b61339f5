import { createClient } from "next-sanity";
import imageUrlBuilder from "@sanity/image-url";

// Get the project ID and dataset from the Sanity Studio config
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID;
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || "production";
const apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || "2023-05-03";

// Validate required environment variables
if (!projectId) {
  console.warn("Missing NEXT_PUBLIC_SANITY_PROJECT_ID environment variable");
}

// Create a client for fetching data
export const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: process.env.NODE_ENV === "production",
  // Add timeout and retry configuration
  requestTagPrefix: "rentersng",
  ignoreBrowserTokenWarning: true,
});

// Set up a helper function for generating image URLs with the Sanity Image URL builder
const builder = imageUrlBuilder(client);

export function urlFor(source: any) {
  return builder.image(source);
}

// Helper function to determine if the current environment is a server environment
export const isServer = typeof window === "undefined";

// GROQ query helpers
export const propertyFields = `
  _id,
  title,
  slug,
  "description": description[0].children[0].text,
  mainImage,
  images,
  propertyType,
  listingType,
  price,
  priceType,
  bedrooms,
  bathrooms,
  area,
  "location": location->{
    _id,
    name,
    slug,
    city,
    state,
    image
  },
  address,
  "features": features[]->{
    _id,
    name,
    icon
  },
  "agent": agent->{
    _id,
    name,
    slug,
    image,
    email,
    phone,
    position
  },
  isFeatured,
  isAvailable,
  publishedAt,
  geoLocation
`;

export const locationFields = `
  _id,
  name,
  slug,
  description,
  image,
  city,
  state,
  isPopular,
  geoLocation
`;

export const testimonialFields = `
  _id,
  name,
  role,
  avatar,
  content,
  rating,
  isActive,
  publishedAt
`;

// Fetch functions with error handling
export async function getFeaturedProperties() {
  try {
    if (!projectId) {
      console.warn("Sanity project ID not configured, returning empty array");
      return [];
    }

    return await client.fetch(
      `*[_type == "property" && isFeatured == true && isAvailable == true] | order(publishedAt desc)[0...4]{
        ${propertyFields}
      }`
    );
  } catch (error) {
    console.error("Error fetching featured properties:", error);
    return [];
  }
}

export async function getPopularLocations() {
  try {
    if (!projectId) {
      console.warn("Sanity project ID not configured, returning empty array");
      return [];
    }

    return await client.fetch(
      `*[_type == "location" && isPopular == true] | order(name asc)[0...6]{
        ${locationFields}
      }`
    );
  } catch (error) {
    console.error("Error fetching popular locations:", error);
    return [];
  }
}

export async function getActiveTestimonials() {
  try {
    if (!projectId) {
      console.warn("Sanity project ID not configured, returning empty array");
      return [];
    }

    return await client.fetch(
      `*[_type == "testimonial" && isActive == true] | order(publishedAt desc){
        ${testimonialFields}
      }`
    );
  } catch (error) {
    console.error("Error fetching testimonials:", error);
    return [];
  }
}

export async function getAllProperties(params = {}) {
  const {
    type = null,
    location = null,
    minPrice = null,
    maxPrice = null,
    bedrooms = null,
    propertyType = null,
    limit = 50,
    offset = 0,
  } = params;

  let query = `*[_type == "property" && isAvailable == true`;

  // Add filters
  if (type && type !== "all") {
    query += ` && listingType == "${type}"`;
  }

  if (location && location !== "all") {
    query += ` && location->slug.current == "${location}"`;
  }

  if (propertyType && propertyType !== "all") {
    query += ` && propertyType == "${propertyType}"`;
  }

  if (minPrice) {
    query += ` && price >= ${minPrice}`;
  }

  if (maxPrice) {
    query += ` && price <= ${maxPrice}`;
  }

  if (bedrooms) {
    if (bedrooms === "4") {
      query += ` && bedrooms >= 4`;
    } else {
      query += ` && bedrooms == ${bedrooms}`;
    }
  }

  query += `] | order(publishedAt desc)[${offset}...${offset + limit}]{
    ${propertyFields}
  }`;

  return client.fetch(query);
}

export async function getPropertyBySlug(slug: string) {
  return client.fetch(
    `*[_type == "property" && slug.current == $slug][0]{
      ${propertyFields}
    }`,
    { slug }
  );
}

export async function getPropertyById(id: string) {
  return client.fetch(
    `*[_type == "property" && _id == $id][0]{
      ${propertyFields}
    }`,
    { id }
  );
}

export async function getLocationBySlug(slug: string) {
  return client.fetch(
    `*[_type == "location" && slug.current == $slug][0]{
      ${locationFields}
    }`,
    { slug }
  );
}

export async function countProperties(params = {}) {
  const {
    type = null,
    location = null,
    minPrice = null,
    maxPrice = null,
    bedrooms = null,
    propertyType = null,
  } = params;

  let query = `count(*[_type == "property" && isAvailable == true`;

  // Add filters
  if (type && type !== "all") {
    query += ` && listingType == "${type}"`;
  }

  if (location && location !== "all") {
    query += ` && location->slug.current == "${location}"`;
  }

  if (propertyType && propertyType !== "all") {
    query += ` && propertyType == "${propertyType}"`;
  }

  if (minPrice) {
    query += ` && price >= ${minPrice}`;
  }

  if (maxPrice) {
    query += ` && price <= ${maxPrice}`;
  }

  if (bedrooms) {
    if (bedrooms === "4") {
      query += ` && bedrooms >= 4`;
    } else {
      query += ` && bedrooms == ${bedrooms}`;
    }
  }

  query += `])`;

  return client.fetch(query);
}
