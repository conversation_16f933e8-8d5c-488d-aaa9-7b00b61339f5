/** @type {import('next').NextConfig} */
const nextConfig = {
  /* config options here */
  reactStrictMode: true,
  transpilePackages: ["react-map-gl", "mapbox-gl"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "j5mmloww.api.sanity.io",
        port: "",
        pathname: "/**",
      },
    ],
  },
  webpack: (config) => {
    // Add a fallback for the mapbox modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      os: false,
    };

    // Add rule to handle HTML files in node modules (for mapbox)
    config.module.rules.push({
      test: /\.html$/,
      use: "ignore-loader",
    });

    // Add rule to handle fs.realpath module
    config.module.rules.push({
      test: /node_modules\/fs\.realpath/,
      use: "ignore-loader",
    });

    // Add rule to handle @mapbox/node-pre-gyp module
    config.module.rules.push({
      test: /node_modules\/@mapbox\/node-pre-gyp\/lib\/util\/nw-pre-gyp\/index\.html$/,
      use: "ignore-loader",
    });

    return config;
  },
  // We're not using Turbopack
};

module.exports = nextConfig;
