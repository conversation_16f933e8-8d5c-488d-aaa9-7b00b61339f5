"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decompress-response";
exports.ids = ["vendor-chunks/decompress-response"];
exports.modules = {

/***/ "(ssr)/./node_modules/decompress-response/index.js":
/*!***************************************************!*\
  !*** ./node_modules/decompress-response/index.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst {Transform, PassThrough} = __webpack_require__(/*! stream */ \"stream\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst mimicResponse = __webpack_require__(/*! mimic-response */ \"(ssr)/./node_modules/mimic-response/index.js\");\n\nmodule.exports = response => {\n\tconst contentEncoding = (response.headers['content-encoding'] || '').toLowerCase();\n\tdelete response.headers['content-encoding'];\n\n\tif (!['gzip', 'deflate', 'br'].includes(contentEncoding)) {\n\t\treturn response;\n\t}\n\n\t// TODO: Remove this when targeting Node.js 12.\n\tconst isBrotli = contentEncoding === 'br';\n\tif (isBrotli && typeof zlib.createBrotliDecompress !== 'function') {\n\t\tresponse.destroy(new Error('Brotli is not supported on Node.js < 12'));\n\t\treturn response;\n\t}\n\n\tlet isEmpty = true;\n\n\tconst checker = new Transform({\n\t\ttransform(data, _encoding, callback) {\n\t\t\tisEmpty = false;\n\n\t\t\tcallback(null, data);\n\t\t},\n\n\t\tflush(callback) {\n\t\t\tcallback();\n\t\t}\n\t});\n\n\tconst finalStream = new PassThrough({\n\t\tautoDestroy: false,\n\t\tdestroy(error, callback) {\n\t\t\tresponse.destroy();\n\n\t\t\tcallback(error);\n\t\t}\n\t});\n\n\tconst decompressStream = isBrotli ? zlib.createBrotliDecompress() : zlib.createUnzip();\n\n\tdecompressStream.once('error', error => {\n\t\tif (isEmpty && !response.readable) {\n\t\t\tfinalStream.end();\n\t\t\treturn;\n\t\t}\n\n\t\tfinalStream.destroy(error);\n\t});\n\n\tmimicResponse(response, finalStream);\n\tresponse.pipe(checker).pipe(decompressStream).pipe(finalStream);\n\n\treturn finalStream;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/decompress-response/index.js\n");

/***/ })

};
;