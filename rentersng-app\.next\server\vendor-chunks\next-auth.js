"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthError: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.AuthError),\n/* harmony export */   CredentialsSignin: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.CredentialsSignin),\n/* harmony export */   customFetch: () => (/* reexport safe */ _auth_core__WEBPACK_IMPORTED_MODULE_0__.customFetch),\n/* harmony export */   \"default\": () => (/* binding */ NextAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var _lib_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/env.js */ \"(action-browser)/./node_modules/next-auth/lib/env.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(action-browser)/./node_modules/next-auth/lib/index.js\");\n/* harmony import */ var _lib_actions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/actions.js */ \"(action-browser)/./node_modules/next-auth/lib/actions.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/core/errors */ \"(action-browser)/./node_modules/@auth/core/errors.js\");\n/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\n\n\n\n\n\n\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nfunction NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n            return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config, (c) => (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, _config);\n            },\n        };\n    }\n    (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(config);\n    const httpHandler = (req) => (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config),\n        signIn: (provider, options, authorizationParams) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, config);\n        },\n        unstable_update: (data) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/lib/actions.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/lib/actions.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(action-browser)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\nasync function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(redirectUrl);\n    return redirectUrl;\n}\nasync function signOut(options, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(res.redirect);\n    return res;\n}\nasync function update(data, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/lib/actions.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/lib/env.js":
/*!*******************************************!*\
  !*** ./node_modules/next-auth/lib/env.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqWithEnvURL: () => (/* binding */ reqWithEnvURL),\n/* harmony export */   setEnvDefaults: () => (/* binding */ setEnvDefaults)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(action-browser)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nfunction reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nfunction setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        (0,_auth_core__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(process.env, config, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/lib/env.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initAuth: () => (/* binding */ initAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(action-browser)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./env.js */ \"(action-browser)/./node_modules/next-auth/lib/env.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\nasync function getSession(headers, config) {\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nfunction initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve((0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = (0,_env_js__WEBPACK_IMPORTED_MODULE_3__.reqWithEnvURL)(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/lib/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/credentials */ \"(action-browser)/./node_modules/@auth/core/providers/credentials.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvcHJvdmlkZXJzL2NyZWRlbnRpYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQ1UiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxSZW50ZXJOZ1xccmVudGVyc25nLWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXHByb3ZpZGVyc1xcY3JlZGVudGlhbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/providers/google.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/google.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/google */ \"(action-browser)/./node_modules/@auth/core/providers/google.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvcHJvdmlkZXJzL2dvb2dsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUNVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcUmVudGVyTmdcXHJlbnRlcnNuZy1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxwcm92aWRlcnNcXGdvb2dsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvZ29vZ2xlXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dvb2dsZVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/providers/google.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthError: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.AuthError),\n/* harmony export */   CredentialsSignin: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.CredentialsSignin),\n/* harmony export */   customFetch: () => (/* reexport safe */ _auth_core__WEBPACK_IMPORTED_MODULE_0__.customFetch),\n/* harmony export */   \"default\": () => (/* binding */ NextAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var _lib_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(rsc)/./node_modules/next-auth/lib/index.js\");\n/* harmony import */ var _lib_actions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/actions.js */ \"(rsc)/./node_modules/next-auth/lib/actions.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/core/errors */ \"(rsc)/./node_modules/@auth/core/errors.js\");\n/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\n\n\n\n\n\n\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nfunction NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n            return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config, (c) => (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, _config);\n            },\n        };\n    }\n    (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(config);\n    const httpHandler = (req) => (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config),\n        signIn: (provider, options, authorizationParams) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, config);\n        },\n        unstable_update: (data) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/actions.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/lib/actions.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\nasync function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(redirectUrl);\n    return redirectUrl;\n}\nasync function signOut(options, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(res.redirect);\n    return res;\n}\nasync function update(data, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2xpYi9hY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF1RTtBQUN2RTtBQUMrRDtBQUMvRDtBQUMyQztBQUNwQyw0Q0FBNEM7QUFDbkQsc0NBQXNDLHFEQUFXO0FBQ2pELFlBQVksdURBQXVEO0FBQ25FO0FBQ0Esc0JBQXNCLDJEQUFlO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHlEQUFRO0FBQ3BCO0FBQ0E7QUFDQSxpQkFBaUIsVUFBVSxHQUFHLFNBQVMsR0FBRyx5Q0FBeUM7QUFDbkY7QUFDQTtBQUNBLGdCQUFnQix1QkFBdUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsVUFBVSxHQUFHLHNCQUFzQixhQUFhLEVBQUU7QUFDekU7QUFDQSxZQUFZLHlEQUFRO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxzQkFBc0I7QUFDN0QsbUNBQW1DLCtCQUErQjtBQUNsRSxzQkFBc0IsZ0RBQUksUUFBUSxjQUFjLDREQUFlLHlEQUFFO0FBQ2pFLDRCQUE0QixxREFBTztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUseURBQVE7QUFDdkI7QUFDQTtBQUNPO0FBQ1Asc0NBQXNDLHFEQUFXO0FBQ2pEO0FBQ0EsZ0JBQWdCLDJEQUFlO0FBQy9CO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxhQUFhO0FBQ3BELG1DQUFtQywrQkFBK0I7QUFDbEUsc0JBQXNCLGdEQUFJLFFBQVEsY0FBYyw0REFBZSx5REFBRTtBQUNqRSw0QkFBNEIscURBQU87QUFDbkM7QUFDQTtBQUNBO0FBQ0EsZUFBZSx5REFBUTtBQUN2QjtBQUNBO0FBQ087QUFDUCxzQ0FBc0MscURBQVc7QUFDakQ7QUFDQSxnQkFBZ0IsMkRBQWU7QUFDL0I7QUFDQTtBQUNBLGtDQUFrQyxNQUFNO0FBQ3hDLG1DQUFtQywrQkFBK0I7QUFDbEUsc0JBQXNCLGdEQUFJLFFBQVEsY0FBYyw0REFBZSx5REFBRTtBQUNqRSw0QkFBNEIscURBQU87QUFDbkM7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxSZW50ZXJOZ1xccmVudGVyc25nLWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXGxpYlxcYWN0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBdXRoLCByYXcsIHNraXBDU1JGQ2hlY2ssIGNyZWF0ZUFjdGlvblVSTCB9IGZyb20gXCJAYXV0aC9jb3JlXCI7XG4vLyBAdHMtZXhwZWN0LWVycm9yIE5leHQuanMgZG9lcyBub3QgeWV0IGNvcnJlY3RseSB1c2UgdGhlIGBwYWNrYWdlLmpzb24jZXhwb3J0c2AgZmllbGRcbmltcG9ydCB7IGhlYWRlcnMgYXMgbmV4dEhlYWRlcnMsIGNvb2tpZXMgfSBmcm9tIFwibmV4dC9oZWFkZXJzXCI7XG4vLyBAdHMtZXhwZWN0LWVycm9yIE5leHQuanMgZG9lcyBub3QgeWV0IGNvcnJlY3RseSB1c2UgdGhlIGBwYWNrYWdlLmpzb24jZXhwb3J0c2AgZmllbGRcbmltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNpZ25Jbihwcm92aWRlciwgb3B0aW9ucyA9IHt9LCBhdXRob3JpemF0aW9uUGFyYW1zLCBjb25maWcpIHtcbiAgICBjb25zdCBoZWFkZXJzID0gbmV3IEhlYWRlcnMoYXdhaXQgbmV4dEhlYWRlcnMoKSk7XG4gICAgY29uc3QgeyByZWRpcmVjdDogc2hvdWxkUmVkaXJlY3QgPSB0cnVlLCByZWRpcmVjdFRvLCAuLi5yZXN0IH0gPSBvcHRpb25zIGluc3RhbmNlb2YgRm9ybURhdGEgPyBPYmplY3QuZnJvbUVudHJpZXMob3B0aW9ucykgOiBvcHRpb25zO1xuICAgIGNvbnN0IGNhbGxiYWNrVXJsID0gcmVkaXJlY3RUbz8udG9TdHJpbmcoKSA/PyBoZWFkZXJzLmdldChcIlJlZmVyZXJcIikgPz8gXCIvXCI7XG4gICAgY29uc3Qgc2lnbkluVVJMID0gY3JlYXRlQWN0aW9uVVJMKFwic2lnbmluXCIsIFxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgYHgtZm9yd2FyZGVkLXByb3RvYCBpcyBub3QgbnVsbGFibGUsIG5leHQuanMgc2V0cyBpdCBieSBkZWZhdWx0XG4gICAgaGVhZGVycy5nZXQoXCJ4LWZvcndhcmRlZC1wcm90b1wiKSwgaGVhZGVycywgcHJvY2Vzcy5lbnYsIGNvbmZpZyk7XG4gICAgaWYgKCFwcm92aWRlcikge1xuICAgICAgICBzaWduSW5VUkwuc2VhcmNoUGFyYW1zLmFwcGVuZChcImNhbGxiYWNrVXJsXCIsIGNhbGxiYWNrVXJsKTtcbiAgICAgICAgaWYgKHNob3VsZFJlZGlyZWN0KVxuICAgICAgICAgICAgcmVkaXJlY3Qoc2lnbkluVVJMLnRvU3RyaW5nKCkpO1xuICAgICAgICByZXR1cm4gc2lnbkluVVJMLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIGxldCB1cmwgPSBgJHtzaWduSW5VUkx9LyR7cHJvdmlkZXJ9PyR7bmV3IFVSTFNlYXJjaFBhcmFtcyhhdXRob3JpemF0aW9uUGFyYW1zKX1gO1xuICAgIGxldCBmb3VuZFByb3ZpZGVyID0ge307XG4gICAgZm9yIChjb25zdCBwcm92aWRlckNvbmZpZyBvZiBjb25maWcucHJvdmlkZXJzKSB7XG4gICAgICAgIGNvbnN0IHsgb3B0aW9ucywgLi4uZGVmYXVsdHMgfSA9IHR5cGVvZiBwcm92aWRlckNvbmZpZyA9PT0gXCJmdW5jdGlvblwiID8gcHJvdmlkZXJDb25maWcoKSA6IHByb3ZpZGVyQ29uZmlnO1xuICAgICAgICBjb25zdCBpZCA9IG9wdGlvbnM/LmlkID8/IGRlZmF1bHRzLmlkO1xuICAgICAgICBpZiAoaWQgPT09IHByb3ZpZGVyKSB7XG4gICAgICAgICAgICBmb3VuZFByb3ZpZGVyID0ge1xuICAgICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICAgIHR5cGU6IG9wdGlvbnM/LnR5cGUgPz8gZGVmYXVsdHMudHlwZSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoIWZvdW5kUHJvdmlkZXIuaWQpIHtcbiAgICAgICAgY29uc3QgdXJsID0gYCR7c2lnbkluVVJMfT8ke25ldyBVUkxTZWFyY2hQYXJhbXMoeyBjYWxsYmFja1VybCB9KX1gO1xuICAgICAgICBpZiAoc2hvdWxkUmVkaXJlY3QpXG4gICAgICAgICAgICByZWRpcmVjdCh1cmwpO1xuICAgICAgICByZXR1cm4gdXJsO1xuICAgIH1cbiAgICBpZiAoZm91bmRQcm92aWRlci50eXBlID09PSBcImNyZWRlbnRpYWxzXCIpIHtcbiAgICAgICAgdXJsID0gdXJsLnJlcGxhY2UoXCJzaWduaW5cIiwgXCJjYWxsYmFja1wiKTtcbiAgICB9XG4gICAgaGVhZGVycy5zZXQoXCJDb250ZW50LVR5cGVcIiwgXCJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWRcIik7XG4gICAgY29uc3QgYm9keSA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoeyAuLi5yZXN0LCBjYWxsYmFja1VybCB9KTtcbiAgICBjb25zdCByZXEgPSBuZXcgUmVxdWVzdCh1cmwsIHsgbWV0aG9kOiBcIlBPU1RcIiwgaGVhZGVycywgYm9keSB9KTtcbiAgICBjb25zdCByZXMgPSBhd2FpdCBBdXRoKHJlcSwgeyAuLi5jb25maWcsIHJhdywgc2tpcENTUkZDaGVjayB9KTtcbiAgICBjb25zdCBjb29raWVKYXIgPSBhd2FpdCBjb29raWVzKCk7XG4gICAgZm9yIChjb25zdCBjIG9mIHJlcz8uY29va2llcyA/PyBbXSlcbiAgICAgICAgY29va2llSmFyLnNldChjLm5hbWUsIGMudmFsdWUsIGMub3B0aW9ucyk7XG4gICAgY29uc3QgcmVzcG9uc2VVcmwgPSByZXMgaW5zdGFuY2VvZiBSZXNwb25zZSA/IHJlcy5oZWFkZXJzLmdldChcIkxvY2F0aW9uXCIpIDogcmVzLnJlZGlyZWN0O1xuICAgIC8vIE5PVEU6IGlmIGZvciBzb21lIHVuZXhwZWN0ZWQgcmVhc29uIHRoZSByZXNwb25zZVVybCBpcyBub3Qgc2V0LFxuICAgIC8vIHdlIHJlZGlyZWN0IHRvIHRoZSBvcmlnaW5hbCB1cmxcbiAgICBjb25zdCByZWRpcmVjdFVybCA9IHJlc3BvbnNlVXJsID8/IHVybDtcbiAgICBpZiAoc2hvdWxkUmVkaXJlY3QpXG4gICAgICAgIHJldHVybiByZWRpcmVjdChyZWRpcmVjdFVybCk7XG4gICAgcmV0dXJuIHJlZGlyZWN0VXJsO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNpZ25PdXQob3B0aW9ucywgY29uZmlnKSB7XG4gICAgY29uc3QgaGVhZGVycyA9IG5ldyBIZWFkZXJzKGF3YWl0IG5leHRIZWFkZXJzKCkpO1xuICAgIGhlYWRlcnMuc2V0KFwiQ29udGVudC1UeXBlXCIsIFwiYXBwbGljYXRpb24veC13d3ctZm9ybS11cmxlbmNvZGVkXCIpO1xuICAgIGNvbnN0IHVybCA9IGNyZWF0ZUFjdGlvblVSTChcInNpZ25vdXRcIiwgXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBgeC1mb3J3YXJkZWQtcHJvdG9gIGlzIG5vdCBudWxsYWJsZSwgbmV4dC5qcyBzZXRzIGl0IGJ5IGRlZmF1bHRcbiAgICBoZWFkZXJzLmdldChcIngtZm9yd2FyZGVkLXByb3RvXCIpLCBoZWFkZXJzLCBwcm9jZXNzLmVudiwgY29uZmlnKTtcbiAgICBjb25zdCBjYWxsYmFja1VybCA9IG9wdGlvbnM/LnJlZGlyZWN0VG8gPz8gaGVhZGVycy5nZXQoXCJSZWZlcmVyXCIpID8/IFwiL1wiO1xuICAgIGNvbnN0IGJvZHkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHsgY2FsbGJhY2tVcmwgfSk7XG4gICAgY29uc3QgcmVxID0gbmV3IFJlcXVlc3QodXJsLCB7IG1ldGhvZDogXCJQT1NUXCIsIGhlYWRlcnMsIGJvZHkgfSk7XG4gICAgY29uc3QgcmVzID0gYXdhaXQgQXV0aChyZXEsIHsgLi4uY29uZmlnLCByYXcsIHNraXBDU1JGQ2hlY2sgfSk7XG4gICAgY29uc3QgY29va2llSmFyID0gYXdhaXQgY29va2llcygpO1xuICAgIGZvciAoY29uc3QgYyBvZiByZXM/LmNvb2tpZXMgPz8gW10pXG4gICAgICAgIGNvb2tpZUphci5zZXQoYy5uYW1lLCBjLnZhbHVlLCBjLm9wdGlvbnMpO1xuICAgIGlmIChvcHRpb25zPy5yZWRpcmVjdCA/PyB0cnVlKVxuICAgICAgICByZXR1cm4gcmVkaXJlY3QocmVzLnJlZGlyZWN0KTtcbiAgICByZXR1cm4gcmVzO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZShkYXRhLCBjb25maWcpIHtcbiAgICBjb25zdCBoZWFkZXJzID0gbmV3IEhlYWRlcnMoYXdhaXQgbmV4dEhlYWRlcnMoKSk7XG4gICAgaGVhZGVycy5zZXQoXCJDb250ZW50LVR5cGVcIiwgXCJhcHBsaWNhdGlvbi9qc29uXCIpO1xuICAgIGNvbnN0IHVybCA9IGNyZWF0ZUFjdGlvblVSTChcInNlc3Npb25cIiwgXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBgeC1mb3J3YXJkZWQtcHJvdG9gIGlzIG5vdCBudWxsYWJsZSwgbmV4dC5qcyBzZXRzIGl0IGJ5IGRlZmF1bHRcbiAgICBoZWFkZXJzLmdldChcIngtZm9yd2FyZGVkLXByb3RvXCIpLCBoZWFkZXJzLCBwcm9jZXNzLmVudiwgY29uZmlnKTtcbiAgICBjb25zdCBib2R5ID0gSlNPTi5zdHJpbmdpZnkoeyBkYXRhIH0pO1xuICAgIGNvbnN0IHJlcSA9IG5ldyBSZXF1ZXN0KHVybCwgeyBtZXRob2Q6IFwiUE9TVFwiLCBoZWFkZXJzLCBib2R5IH0pO1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IEF1dGgocmVxLCB7IC4uLmNvbmZpZywgcmF3LCBza2lwQ1NSRkNoZWNrIH0pO1xuICAgIGNvbnN0IGNvb2tpZUphciA9IGF3YWl0IGNvb2tpZXMoKTtcbiAgICBmb3IgKGNvbnN0IGMgb2YgcmVzPy5jb29raWVzID8/IFtdKVxuICAgICAgICBjb29raWVKYXIuc2V0KGMubmFtZSwgYy52YWx1ZSwgYy5vcHRpb25zKTtcbiAgICByZXR1cm4gcmVzLmJvZHk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/actions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/env.js":
/*!*******************************************!*\
  !*** ./node_modules/next-auth/lib/env.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqWithEnvURL: () => (/* binding */ reqWithEnvURL),\n/* harmony export */   setEnvDefaults: () => (/* binding */ setEnvDefaults)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nfunction reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nfunction setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        (0,_auth_core__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(process.env, config, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initAuth: () => (/* binding */ initAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\nasync function getSession(headers, config) {\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nfunction initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve((0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = (0,_env_js__WEBPACK_IMPORTED_MODULE_3__.reqWithEnvURL)(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/credentials */ \"(rsc)/./node_modules/@auth/core/providers/credentials.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcUmVudGVyTmdcXHJlbnRlcnNuZy1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxwcm92aWRlcnNcXGNyZWRlbnRpYWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJAYXV0aC9jb3JlL3Byb3ZpZGVycy9jcmVkZW50aWFsc1wiO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gXCJAYXV0aC9jb3JlL3Byb3ZpZGVycy9jcmVkZW50aWFsc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/google.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/google.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/google */ \"(rsc)/./node_modules/@auth/core/providers/google.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFJlbnRlck5nXFxyZW50ZXJzbmctYXBwXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxccHJvdmlkZXJzXFxnb29nbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dvb2dsZVwiO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gXCJAYXV0aC9jb3JlL3Byb3ZpZGVycy9nb29nbGVcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/google.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(ssr)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ \n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...req?.headers?.cookie ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2xpYi9jbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7b0hBQytCO0FBQ2U7QUFDOUMsVUFBVSxHQUNWLE1BQU1FLHlCQUF5QkQsd0RBQVNBO0FBQ3hDO0FBQ0EsVUFBVSxHQUNILE1BQU1FLDJCQUEyQkYsd0RBQVNBO0FBQ2pEO0FBQ0EsNkRBQTZEO0FBQzdEOzs7Ozs7O0NBT0MsR0FDTSxlQUFlRyxVQUFVQyxJQUFJLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFQyxNQUFNLENBQUMsQ0FBQztJQUM5RCxNQUFNQyxNQUFNLEdBQUdDLFdBQVdKLFlBQVksQ0FBQyxFQUFFRCxNQUFNO0lBQy9DLElBQUk7UUFDQSxNQUFNTSxVQUFVO1lBQ1pDLFNBQVM7Z0JBQ0wsZ0JBQWdCO2dCQUNoQixHQUFJSixLQUFLSSxTQUFTQyxTQUFTO29CQUFFQSxRQUFRTCxJQUFJSSxPQUFPLENBQUNDLE1BQU07Z0JBQUMsSUFBSSxDQUFDLENBQUM7WUFDbEU7UUFDSjtRQUNBLElBQUlMLEtBQUtNLE1BQU07WUFDWEgsUUFBUUcsSUFBSSxHQUFHQyxLQUFLQyxTQUFTLENBQUNSLElBQUlNLElBQUk7WUFDdENILFFBQVFNLE1BQU0sR0FBRztRQUNyQjtRQUNBLE1BQU1DLE1BQU0sTUFBTUMsTUFBTVYsS0FBS0U7UUFDN0IsTUFBTVMsT0FBTyxNQUFNRixJQUFJRyxJQUFJO1FBQzNCLElBQUksQ0FBQ0gsSUFBSUksRUFBRSxFQUNQLE1BQU1GO1FBQ1YsT0FBT0E7SUFDWCxFQUNBLE9BQU9HLE9BQU87UUFDVmhCLE9BQU9nQixLQUFLLENBQUMsSUFBSXJCLGlCQUFpQnFCLE1BQU1DLE9BQU8sRUFBRUQ7UUFDakQsT0FBTztJQUNYO0FBQ0o7QUFDQSxjQUFjLEdBQ1AsU0FBU2IsV0FBV0osVUFBVTtJQUNqQyxJQUFJLElBQTZCLEVBQUU7UUFDL0IsK0NBQStDO1FBQy9DLE9BQU8sR0FBR0EsV0FBV21CLGFBQWEsR0FBR25CLFdBQVdvQixjQUFjLEVBQUU7SUFDcEU7SUFDQSwrQ0FBK0M7SUFDL0MsT0FBT3BCLFdBQVdxQixRQUFRO0FBQzlCO0FBQ0EsZUFBZSxHQUNSLFNBQVNDO0lBQ1osTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUc5QiwyQ0FBYyxDQUFDLE9BQU9nQyxjQUFjLGNBQWNBLFVBQVVDLE1BQU0sR0FBRztJQUNyRyxNQUFNQyxZQUFZLElBQU1KLFlBQVk7SUFDcEMsTUFBTUssYUFBYSxJQUFNTCxZQUFZO0lBQ3JDOUIsNENBQWU7K0JBQUM7WUFDWnFDLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVKO1lBQ2xDRyxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXSDtZQUNuQzt1Q0FBTztvQkFDSEUsT0FBT0UsbUJBQW1CLENBQUMsVUFBVUw7b0JBQ3JDRyxPQUFPRSxtQkFBbUIsQ0FBQyxXQUFXSjtnQkFDMUM7O1FBQ0o7OEJBQUcsRUFBRTtJQUNMLE9BQU9OO0FBQ1g7QUFDQTs7O0NBR0MsR0FDTSxTQUFTVztJQUNaLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0gsR0FBRyxLQUFLO0FBQ25DO0FBQ0E7OztDQUdDLEdBQ00sU0FBU0ksU0FBU25DLEdBQUc7SUFDeEIsTUFBTW9DLGFBQWEsSUFBSUMsSUFBSTtJQUMzQixJQUFJckMsT0FBTyxDQUFDQSxJQUFJc0MsVUFBVSxDQUFDLFNBQVM7UUFDaEN0QyxNQUFNLENBQUMsUUFBUSxFQUFFQSxLQUFLO0lBQzFCO0lBQ0EsTUFBTXVDLE9BQU8sSUFBSUYsSUFBSXJDLE9BQU9vQztJQUM1QixNQUFNeEMsT0FBTyxDQUFDMkMsS0FBS0MsUUFBUSxLQUFLLE1BQU1KLFdBQVdJLFFBQVEsR0FBR0QsS0FBS0MsUUFBUSxDQUNyRSx3QkFBd0I7S0FDdkJDLE9BQU8sQ0FBQyxPQUFPO0lBQ3BCLE1BQU1DLE9BQU8sR0FBR0gsS0FBS0ksTUFBTSxHQUFHL0MsTUFBTTtJQUNwQyxPQUFPO1FBQ0grQyxRQUFRSixLQUFLSSxNQUFNO1FBQ25CQyxNQUFNTCxLQUFLSyxJQUFJO1FBQ2ZoRDtRQUNBOEM7UUFDQUcsVUFBVSxJQUFNSDtJQUNwQjtBQUNKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcUmVudGVyTmdcXHJlbnRlcnNuZy1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxsaWJcXGNsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQXV0aEVycm9yIH0gZnJvbSBcIkBhdXRoL2NvcmUvZXJyb3JzXCI7XG4vKiogQHRvZG8gKi9cbmNsYXNzIENsaWVudEZldGNoRXJyb3IgZXh0ZW5kcyBBdXRoRXJyb3Ige1xufVxuLyoqIEB0b2RvICovXG5leHBvcnQgY2xhc3MgQ2xpZW50U2Vzc2lvbkVycm9yIGV4dGVuZHMgQXV0aEVycm9yIHtcbn1cbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSBJbnRlcm5hbCAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbi8qKlxuICogSWYgcGFzc2VkICdhcHBDb250ZXh0JyB2aWEgZ2V0SW5pdGlhbFByb3BzKCkgaW4gX2FwcC5qc1xuICogdGhlbiBnZXQgdGhlIHJlcSBvYmplY3QgZnJvbSBjdHggYW5kIHVzZSB0aGF0IGZvciB0aGVcbiAqIHJlcSB2YWx1ZSB0byBhbGxvdyBgZmV0Y2hEYXRhYCB0b1xuICogd29yayBzZWVtbGVzc2x5IGluIGdldEluaXRpYWxQcm9wcygpIG9uIHNlcnZlciBzaWRlXG4gKiBwYWdlcyAqYW5kKiBpbiBfYXBwLmpzLlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBmZXRjaERhdGEocGF0aCwgX19ORVhUQVVUSCwgbG9nZ2VyLCByZXEgPSB7fSkge1xuICAgIGNvbnN0IHVybCA9IGAke2FwaUJhc2VVcmwoX19ORVhUQVVUSCl9LyR7cGF0aH1gO1xuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgICAgICAgICAgLi4uKHJlcT8uaGVhZGVycz8uY29va2llID8geyBjb29raWU6IHJlcS5oZWFkZXJzLmNvb2tpZSB9IDoge30pLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHJlcT8uYm9keSkge1xuICAgICAgICAgICAgb3B0aW9ucy5ib2R5ID0gSlNPTi5zdHJpbmdpZnkocmVxLmJvZHkpO1xuICAgICAgICAgICAgb3B0aW9ucy5tZXRob2QgPSBcIlBPU1RcIjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaCh1cmwsIG9wdGlvbnMpO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzLmpzb24oKTtcbiAgICAgICAgaWYgKCFyZXMub2spXG4gICAgICAgICAgICB0aHJvdyBkYXRhO1xuICAgICAgICByZXR1cm4gZGF0YTtcbiAgICB9XG4gICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGxvZ2dlci5lcnJvcihuZXcgQ2xpZW50RmV0Y2hFcnJvcihlcnJvci5tZXNzYWdlLCBlcnJvcikpO1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG59XG4vKiogQGludGVybmFsICovXG5leHBvcnQgZnVuY3Rpb24gYXBpQmFzZVVybChfX05FWFRBVVRIKSB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgLy8gUmV0dXJuIGFic29sdXRlIHBhdGggd2hlbiBjYWxsZWQgc2VydmVyIHNpZGVcbiAgICAgICAgcmV0dXJuIGAke19fTkVYVEFVVEguYmFzZVVybFNlcnZlcn0ke19fTkVYVEFVVEguYmFzZVBhdGhTZXJ2ZXJ9YDtcbiAgICB9XG4gICAgLy8gUmV0dXJuIHJlbGF0aXZlIHBhdGggd2hlbiBjYWxsZWQgY2xpZW50IHNpZGVcbiAgICByZXR1cm4gX19ORVhUQVVUSC5iYXNlUGF0aDtcbn1cbi8qKiBAaW50ZXJuYWwgICovXG5leHBvcnQgZnVuY3Rpb24gdXNlT25saW5lKCkge1xuICAgIGNvbnN0IFtpc09ubGluZSwgc2V0SXNPbmxpbmVdID0gUmVhY3QudXNlU3RhdGUodHlwZW9mIG5hdmlnYXRvciAhPT0gXCJ1bmRlZmluZWRcIiA/IG5hdmlnYXRvci5vbkxpbmUgOiBmYWxzZSk7XG4gICAgY29uc3Qgc2V0T25saW5lID0gKCkgPT4gc2V0SXNPbmxpbmUodHJ1ZSk7XG4gICAgY29uc3Qgc2V0T2ZmbGluZSA9ICgpID0+IHNldElzT25saW5lKGZhbHNlKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcIm9ubGluZVwiLCBzZXRPbmxpbmUpO1xuICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcIm9mZmxpbmVcIiwgc2V0T2ZmbGluZSk7XG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm9ubGluZVwiLCBzZXRPbmxpbmUpO1xuICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvZmZsaW5lXCIsIHNldE9mZmxpbmUpO1xuICAgICAgICB9O1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4gaXNPbmxpbmU7XG59XG4vKipcbiAqIFJldHVybnMgdGhlIG51bWJlciBvZiBzZWNvbmRzIGVsYXBzZWQgc2luY2UgSmFudWFyeSAxLCAxOTcwIDAwOjAwOjAwIFVUQy5cbiAqIEBpbnRlcm5hbFxuICovXG5leHBvcnQgZnVuY3Rpb24gbm93KCkge1xuICAgIHJldHVybiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcbn1cbi8qKlxuICogUmV0dXJucyBhbiBgVVJMYCBsaWtlIG9iamVjdCB0byBtYWtlIHJlcXVlc3RzL3JlZGlyZWN0cyBmcm9tIHNlcnZlci1zaWRlXG4gKiBAaW50ZXJuYWxcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlVXJsKHVybCkge1xuICAgIGNvbnN0IGRlZmF1bHRVcmwgPSBuZXcgVVJMKFwiaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaS9hdXRoXCIpO1xuICAgIGlmICh1cmwgJiYgIXVybC5zdGFydHNXaXRoKFwiaHR0cFwiKSkge1xuICAgICAgICB1cmwgPSBgaHR0cHM6Ly8ke3VybH1gO1xuICAgIH1cbiAgICBjb25zdCBfdXJsID0gbmV3IFVSTCh1cmwgfHwgZGVmYXVsdFVybCk7XG4gICAgY29uc3QgcGF0aCA9IChfdXJsLnBhdGhuYW1lID09PSBcIi9cIiA/IGRlZmF1bHRVcmwucGF0aG5hbWUgOiBfdXJsLnBhdGhuYW1lKVxuICAgICAgICAvLyBSZW1vdmUgdHJhaWxpbmcgc2xhc2hcbiAgICAgICAgLnJlcGxhY2UoL1xcLyQvLCBcIlwiKTtcbiAgICBjb25zdCBiYXNlID0gYCR7X3VybC5vcmlnaW59JHtwYXRofWA7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgb3JpZ2luOiBfdXJsLm9yaWdpbixcbiAgICAgICAgaG9zdDogX3VybC5ob3N0LFxuICAgICAgICBwYXRoLFxuICAgICAgICBiYXNlLFxuICAgICAgICB0b1N0cmluZzogKCkgPT4gYmFzZSxcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQXV0aEVycm9yIiwiQ2xpZW50RmV0Y2hFcnJvciIsIkNsaWVudFNlc3Npb25FcnJvciIsImZldGNoRGF0YSIsInBhdGgiLCJfX05FWFRBVVRIIiwibG9nZ2VyIiwicmVxIiwidXJsIiwiYXBpQmFzZVVybCIsIm9wdGlvbnMiLCJoZWFkZXJzIiwiY29va2llIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJtZXRob2QiLCJyZXMiLCJmZXRjaCIsImRhdGEiLCJqc29uIiwib2siLCJlcnJvciIsIm1lc3NhZ2UiLCJiYXNlVXJsU2VydmVyIiwiYmFzZVBhdGhTZXJ2ZXIiLCJiYXNlUGF0aCIsInVzZU9ubGluZSIsImlzT25saW5lIiwic2V0SXNPbmxpbmUiLCJ1c2VTdGF0ZSIsIm5hdmlnYXRvciIsIm9uTGluZSIsInNldE9ubGluZSIsInNldE9mZmxpbmUiLCJ1c2VFZmZlY3QiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsIm5vdyIsIk1hdGgiLCJmbG9vciIsIkRhdGUiLCJwYXJzZVVybCIsImRlZmF1bHRVcmwiLCJVUkwiLCJzdGFydHNXaXRoIiwiX3VybCIsInBhdGhuYW1lIiwicmVwbGFjZSIsImJhc2UiLCJvcmlnaW4iLCJob3N0IiwidG9TdHJpbmciXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(ssr)/./node_modules/next-auth/lib/client.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ \n\n\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                })}`;\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href } = options ?? {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async ({ event } = {})=>{\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react.js\n");

/***/ })

};
;