/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/properties/route";
exports.ids = ["app/api/properties/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_USER_Documents_augment_projects_RenterNg_rentersng_app_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/properties/route.ts */ \"(rsc)/./src/app/api/properties/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/properties/route\",\n        pathname: \"/api/properties\",\n        filename: \"route\",\n        bundlePath: \"app/api/properties/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\RenterNg\\\\rentersng-app\\\\src\\\\app\\\\api\\\\properties\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_USER_Documents_augment_projects_RenterNg_rentersng_app_src_app_api_properties_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/properties/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/properties/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/auth */ \"(rsc)/./src/auth.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Parse query parameters\n        const take = searchParams.get(\"take\") ? parseInt(searchParams.get(\"take\")) : 10;\n        const skip = searchParams.get(\"skip\") ? parseInt(searchParams.get(\"skip\")) : 0;\n        const listingType = searchParams.get(\"listingType\");\n        const propertyType = searchParams.get(\"propertyType\");\n        const minPrice = searchParams.get(\"minPrice\") ? parseFloat(searchParams.get(\"minPrice\")) : undefined;\n        const maxPrice = searchParams.get(\"maxPrice\") ? parseFloat(searchParams.get(\"maxPrice\")) : undefined;\n        const bedrooms = searchParams.get(\"bedrooms\") ? parseInt(searchParams.get(\"bedrooms\")) : undefined;\n        const city = searchParams.get(\"city\") || undefined;\n        const state = searchParams.get(\"state\") || undefined;\n        const featured = searchParams.get(\"featured\") === \"true\" ? true : undefined;\n        // Default to published=true unless explicitly set to false\n        const published = searchParams.get(\"published\") === \"false\" ? false : true; // Always default to true for better visibility\n        // Owner ID filtering (requires authentication check)\n        let ownerId = searchParams.get(\"ownerId\") || undefined;\n        // If ownerId is specified, verify the user is authorized to view these properties\n        if (ownerId) {\n            const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_2__.auth)();\n            // If not authenticated or not the owner (and not an admin), don't allow filtering by owner\n            if (!session || !session.user || session.user.id !== ownerId && session.user.role !== \"ADMIN\") {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Unauthorized to view properties for this owner\"\n                }, {\n                    status: 403\n                });\n            }\n        }\n        // Sorting parameters\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        // Log the search parameters\n        console.log(\"API: Fetching properties with params:\", {\n            take,\n            skip,\n            listingType,\n            propertyType,\n            minPrice,\n            maxPrice,\n            bedrooms,\n            city,\n            state,\n            featured,\n            published,\n            ownerId,\n            sortBy,\n            sortOrder\n        });\n        // Get properties from database\n        const result = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.getProperties)({\n            take,\n            skip,\n            listingType,\n            propertyType,\n            minPrice,\n            maxPrice,\n            bedrooms,\n            city,\n            state,\n            featured,\n            published,\n            ownerId,\n            sortBy,\n            sortOrder\n        });\n        console.log(`API: Found ${result.properties.length} properties`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error(\"Error fetching properties:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch properties\",\n            message: error instanceof Error ? error.message : String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/properties/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/auth.config.ts":
/*!****************************!*\
  !*** ./src/auth.config.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/schemas/auth */ \"(rsc)/./src/schemas/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcrypt */ \"bcrypt\");\n/* harmony import */ var bcrypt__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcrypt__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_mock_db__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/mock-db */ \"(rsc)/./src/lib/mock-db.ts\");\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n            profile (profile) {\n                return {\n                    id: profile.sub,\n                    name: profile.name,\n                    email: profile.email,\n                    image: profile.picture,\n                    role: _lib_mock_db__WEBPACK_IMPORTED_MODULE_5__.UserRole.USER\n                };\n            }\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            async authorize (credentials) {\n                const validatedFields = _schemas_auth__WEBPACK_IMPORTED_MODULE_2__.LoginSchema.safeParse(credentials);\n                if (validatedFields.success) {\n                    const { email, password } = validatedFields.data;\n                    try {\n                        const user = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_3__.getUserByEmail)(email);\n                        if (!user || !user.password) return null;\n                        const passwordsMatch = await bcrypt__WEBPACK_IMPORTED_MODULE_4___default().compare(password, user.password);\n                        if (passwordsMatch) return user;\n                    } catch (error) {\n                        console.error(\"Error during credential authorization:\", error);\n                        return null;\n                    }\n                }\n                return null;\n            }\n        })\n    ],\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                // Only proceed for OAuth providers\n                if (account?.provider !== \"google\") return true;\n                if (user.email) {\n                    try {\n                        const existingUser = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_3__.getUserByEmail)(user.email);\n                        // If user doesn't exist, create a new one\n                        if (!existingUser && account.provider === \"google\") {\n                            await (0,_lib_db__WEBPACK_IMPORTED_MODULE_3__.createUser)({\n                                email: user.email,\n                                name: user.name,\n                                image: user.image,\n                                role: _lib_mock_db__WEBPACK_IMPORTED_MODULE_5__.UserRole.USER\n                            });\n                        }\n                    } catch (error) {\n                        console.error(\"Error during OAuth sign in:\", error);\n                    // Continue with sign in even if there's a database error\n                    }\n                }\n                return true;\n            } catch (error) {\n                console.error(\"Unexpected error during sign in:\", error);\n                return true; // Allow sign in even if there's an error\n            }\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\",\n        error: \"/auth/error\"\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/auth.config.ts\n");

/***/ }),

/***/ "(rsc)/./src/auth.ts":
/*!*********************!*\
  !*** ./src/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _auth_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/auth.config */ \"(rsc)/./src/auth.config.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\n\n\nconst { handlers: { GET, POST }, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    session: {\n        strategy: \"jwt\"\n    },\n    ..._auth_config__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    callbacks: {\n        async session ({ token, session }) {\n            if (token.sub && session.user) {\n                session.user.id = token.sub;\n            }\n            if (token.role && session.user) {\n                session.user.role = token.role;\n            }\n            if (token.name && session.user) {\n                session.user.name = token.name;\n            }\n            if (token.email && session.user) {\n                session.user.email = token.email;\n            }\n            if (token.picture && session.user) {\n                session.user.image = token.picture;\n            }\n            return session;\n        },\n        async jwt ({ token, user, account, profile }) {\n            try {\n                // If user just signed in, update token with user data\n                if (user) {\n                    token.role = user.role;\n                    token.name = user.name;\n                    token.email = user.email;\n                    token.picture = user.image;\n                } else if (token.sub) {\n                    try {\n                        // If not a new sign-in, fetch latest user data\n                        const existingUser = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_4__.getUserById)(token.sub);\n                        if (existingUser) {\n                            token.role = existingUser.role;\n                            token.name = existingUser.name;\n                            token.email = existingUser.email;\n                            token.picture = existingUser.image;\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching user data for JWT:\", error);\n                    // Continue with existing token data\n                    }\n                }\n                return token;\n            } catch (error) {\n                console.error(\"Unexpected error in JWT callback:\", error);\n                return token; // Return the token as is\n            }\n        },\n        // Merge with callbacks from auth.config.ts\n        ..._auth_config__WEBPACK_IMPORTED_MODULE_3__[\"default\"].callbacks || {}\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createProperty: () => (/* binding */ createProperty),\n/* harmony export */   createPropertyComment: () => (/* binding */ createPropertyComment),\n/* harmony export */   createPropertyRating: () => (/* binding */ createPropertyRating),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deleteProperty: () => (/* binding */ deleteProperty),\n/* harmony export */   deletePropertyComment: () => (/* binding */ deletePropertyComment),\n/* harmony export */   deletePropertyRating: () => (/* binding */ deletePropertyRating),\n/* harmony export */   getProperties: () => (/* binding */ getProperties),\n/* harmony export */   getPropertyAverageRating: () => (/* binding */ getPropertyAverageRating),\n/* harmony export */   getPropertyById: () => (/* binding */ getPropertyById),\n/* harmony export */   getPropertyComments: () => (/* binding */ getPropertyComments),\n/* harmony export */   getPropertyRatings: () => (/* binding */ getPropertyRatings),\n/* harmony export */   getSavedProperties: () => (/* binding */ getSavedProperties),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserPropertyRating: () => (/* binding */ getUserPropertyRating),\n/* harmony export */   isPropertySaved: () => (/* binding */ isPropertySaved),\n/* harmony export */   saveProperty: () => (/* binding */ saveProperty),\n/* harmony export */   unsaveProperty: () => (/* binding */ unsaveProperty),\n/* harmony export */   updateProperty: () => (/* binding */ updateProperty),\n/* harmony export */   updatePropertyComment: () => (/* binding */ updatePropertyComment),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _mock_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mock-db */ \"(rsc)/./src/lib/mock-db.ts\");\n\n\n// Determine if we should use mock database\nconst useMockDb =  true && (process.env.USE_MOCK_DB === \"true\" || !process.env.DATABASE_URL);\n// User related functions\nasync function getUserById(id) {\n    if (useMockDb) {\n        return (0,_mock_db__WEBPACK_IMPORTED_MODULE_1__.mockGetUserById)(id);\n    }\n    try {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id\n            },\n            include: {\n                savedProperties: {\n                    include: {\n                        property: true\n                    }\n                }\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching user by ID:\", error);\n        return null;\n    }\n}\nasync function getUserByEmail(email) {\n    if (useMockDb) {\n        return (0,_mock_db__WEBPACK_IMPORTED_MODULE_1__.mockGetUserByEmail)(email);\n    }\n    try {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                email\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching user by email:\", error);\n        return null;\n    }\n}\nasync function createUser(data) {\n    if (useMockDb) {\n        return (0,_mock_db__WEBPACK_IMPORTED_MODULE_1__.mockCreateUser)(data);\n    }\n    try {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.create({\n            data\n        });\n    } catch (error) {\n        console.error(\"Error creating user:\", error);\n        return null;\n    }\n}\nasync function updateUser(id, data) {\n    if (useMockDb) {\n        // Simple mock implementation for development\n        console.log(`Mock: Updating user ${id} with data:`, data);\n        return {\n            id,\n            ...data\n        };\n    }\n    try {\n        return await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.user.update({\n            where: {\n                id\n            },\n            data\n        });\n    } catch (error) {\n        console.error(\"Error updating user:\", error);\n        return null;\n    }\n}\n// Property related functions\nasync function getProperties(params) {\n    const { take = 10, skip = 0, listingType, propertyType, minPrice, maxPrice, bedrooms, city, state, featured, published, ownerId, sortBy = \"createdAt\", sortOrder = \"desc\" } = params;\n    const where = {};\n    // Default to published=true unless explicitly set to false\n    where.published = published !== false;\n    if (listingType) where.listingType = listingType;\n    if (propertyType) where.propertyType = propertyType;\n    if (minPrice) {\n        where.price = {\n            ...where.price || {},\n            gte: minPrice\n        };\n    }\n    if (maxPrice) {\n        where.price = {\n            ...where.price || {},\n            lte: maxPrice\n        };\n    }\n    if (bedrooms) {\n        if (bedrooms >= 4) {\n            where.bedrooms = {\n                gte: 4\n            };\n        } else {\n            where.bedrooms = bedrooms;\n        }\n    }\n    if (city) where.city = {\n        contains: city,\n        mode: \"insensitive\"\n    };\n    if (state) where.state = {\n        contains: state,\n        mode: \"insensitive\"\n    };\n    if (featured !== undefined) where.featured = featured;\n    if (ownerId) where.ownerId = ownerId;\n    // Validate sortBy to prevent injection\n    const validSortFields = [\n        \"createdAt\",\n        \"updatedAt\",\n        \"price\",\n        \"title\",\n        \"bedrooms\",\n        \"bathrooms\",\n        \"area\"\n    ];\n    const orderBy = {};\n    if (validSortFields.includes(sortBy)) {\n        orderBy[sortBy] = sortOrder;\n    } else {\n        // Default to createdAt if invalid sort field\n        orderBy.createdAt = \"desc\";\n    }\n    console.log(\"DB: Fetching properties with where clause:\", where);\n    console.log(\"DB: Sorting by:\", orderBy);\n    try {\n        const [properties, total] = await Promise.all([\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.findMany({\n                where,\n                take,\n                skip,\n                orderBy,\n                include: {\n                    owner: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            image: true\n                        }\n                    }\n                }\n            }),\n            _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.count({\n                where\n            })\n        ]);\n        console.log(`DB: Found ${properties.length} properties out of ${total} total`);\n        if (properties.length > 0) {\n            console.log(\"DB: First property ID:\", properties[0].id);\n        }\n        return {\n            properties,\n            total\n        };\n    } catch (error) {\n        console.error(\"DB: Error fetching properties:\", error);\n        throw error;\n    }\n}\nasync function getPropertyById(id) {\n    console.log(`DB: Attempting to find property with ID: ${id}`);\n    try {\n        const property = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.findUnique({\n            where: {\n                id\n            },\n            include: {\n                owner: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        image: true\n                    }\n                }\n            }\n        });\n        if (property) {\n            console.log(`DB: Property found with ID: ${id}`);\n            // Get average rating\n            const ratingData = await getPropertyAverageRating(id);\n            return {\n                ...property,\n                averageRating: ratingData.averageRating,\n                totalRatings: ratingData.totalRatings\n            };\n        }\n        console.log(`DB: No property found with ID: ${id}`);\n        return property;\n    } catch (error) {\n        console.error(`DB: Error finding property with ID: ${id}`, error);\n        throw error;\n    }\n}\nasync function createProperty(data) {\n    console.log(\"DB: Creating new property with data:\", {\n        title: data.title,\n        ownerId: data.ownerId,\n        published: data.published,\n        listingType: data.listingType,\n        propertyType: data.propertyType\n    });\n    try {\n        const property = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.create({\n            data\n        });\n        console.log(`DB: Property created successfully with ID: ${property.id}`);\n        return property;\n    } catch (error) {\n        console.error(\"DB: Error creating property:\", error);\n        throw error;\n    }\n}\nasync function updateProperty(id, data) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.update({\n        where: {\n            id\n        },\n        data\n    });\n}\nasync function deleteProperty(id) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.property.delete({\n        where: {\n            id\n        }\n    });\n}\n// Saved properties functions\nasync function saveProperty(userId, propertyId) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.savedProperty.create({\n        data: {\n            userId,\n            propertyId\n        }\n    });\n}\nasync function unsaveProperty(userId, propertyId) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.savedProperty.deleteMany({\n        where: {\n            userId,\n            propertyId\n        }\n    });\n}\nasync function getSavedProperties(userId) {\n    return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.savedProperty.findMany({\n        where: {\n            userId\n        },\n        include: {\n            property: {\n                include: {\n                    owner: {\n                        select: {\n                            id: true,\n                            name: true,\n                            email: true,\n                            image: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n}\nasync function isPropertySaved(userId, propertyId) {\n    const savedProperty = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.savedProperty.findFirst({\n        where: {\n            userId,\n            propertyId\n        }\n    });\n    return !!savedProperty;\n}\n// Property rating functions\nasync function createPropertyRating(data) {\n    try {\n        // Check if user has already rated this property\n        const existingRating = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.findUnique({\n            where: {\n                userId_propertyId: {\n                    userId: data.userId,\n                    propertyId: data.propertyId\n                }\n            }\n        });\n        if (existingRating) {\n            // Update existing rating\n            return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.update({\n                where: {\n                    id: existingRating.id\n                },\n                data: {\n                    rating: data.rating\n                }\n            });\n        }\n        // Create new rating\n        return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.create({\n            data\n        });\n    } catch (error) {\n        console.error(\"Error creating property rating:\", error);\n        return null;\n    }\n}\nasync function getPropertyRatings(propertyId) {\n    try {\n        const ratings = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.findMany({\n            where: {\n                propertyId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        image: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        return ratings;\n    } catch (error) {\n        console.error(\"Error fetching property ratings:\", error);\n        return [];\n    }\n}\nasync function getPropertyAverageRating(propertyId) {\n    try {\n        const result = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.aggregate({\n            where: {\n                propertyId\n            },\n            _avg: {\n                rating: true\n            },\n            _count: true\n        });\n        return {\n            averageRating: result._avg.rating || 0,\n            totalRatings: result._count\n        };\n    } catch (error) {\n        console.error(\"Error calculating average rating:\", error);\n        return {\n            averageRating: 0,\n            totalRatings: 0\n        };\n    }\n}\nasync function getUserPropertyRating(userId, propertyId) {\n    try {\n        const rating = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.findUnique({\n            where: {\n                userId_propertyId: {\n                    userId,\n                    propertyId\n                }\n            }\n        });\n        return rating;\n    } catch (error) {\n        console.error(\"Error fetching user property rating:\", error);\n        return null;\n    }\n}\nasync function deletePropertyRating(id) {\n    try {\n        return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyRating.delete({\n            where: {\n                id\n            }\n        });\n    } catch (error) {\n        console.error(\"Error deleting property rating:\", error);\n        return null;\n    }\n}\n// Property comment functions\nasync function createPropertyComment(data) {\n    try {\n        return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyComment.create({\n            data\n        });\n    } catch (error) {\n        console.error(\"Error creating property comment:\", error);\n        return null;\n    }\n}\nasync function getPropertyComments(propertyId) {\n    try {\n        const comments = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyComment.findMany({\n            where: {\n                propertyId\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        name: true,\n                        email: true,\n                        image: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        return comments;\n    } catch (error) {\n        console.error(\"Error fetching property comments:\", error);\n        return [];\n    }\n}\nasync function updatePropertyComment(id, data) {\n    try {\n        return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyComment.update({\n            where: {\n                id\n            },\n            data\n        });\n    } catch (error) {\n        console.error(\"Error updating property comment:\", error);\n        return null;\n    }\n}\nasync function deletePropertyComment(id) {\n    try {\n        return _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.propertyComment.delete({\n            where: {\n                id\n            }\n        });\n    } catch (error) {\n        console.error(\"Error deleting property comment:\", error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mock-db.ts":
/*!****************************!*\
  !*** ./src/lib/mock-db.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListingType: () => (/* binding */ ListingType),\n/* harmony export */   PriceType: () => (/* binding */ PriceType),\n/* harmony export */   PropertyType: () => (/* binding */ PropertyType),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   mockCreateUser: () => (/* binding */ mockCreateUser),\n/* harmony export */   mockGetUserByEmail: () => (/* binding */ mockGetUserByEmail),\n/* harmony export */   mockGetUserById: () => (/* binding */ mockGetUserById),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n// Define enums to match Prisma's enums\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"USER\"] = \"USER\";\n    UserRole[\"AGENT\"] = \"AGENT\";\n    UserRole[\"LANDLORD\"] = \"LANDLORD\";\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n    return UserRole;\n}({});\nvar ListingType = /*#__PURE__*/ function(ListingType) {\n    ListingType[\"RENT\"] = \"RENT\";\n    ListingType[\"SALE\"] = \"SALE\";\n    return ListingType;\n}({});\nvar PropertyType = /*#__PURE__*/ function(PropertyType) {\n    PropertyType[\"APARTMENT\"] = \"APARTMENT\";\n    PropertyType[\"HOUSE\"] = \"HOUSE\";\n    PropertyType[\"DUPLEX\"] = \"DUPLEX\";\n    PropertyType[\"STUDIO\"] = \"STUDIO\";\n    PropertyType[\"VILLA\"] = \"VILLA\";\n    PropertyType[\"OFFICE\"] = \"OFFICE\";\n    PropertyType[\"SHOP\"] = \"SHOP\";\n    PropertyType[\"WAREHOUSE\"] = \"WAREHOUSE\";\n    PropertyType[\"LAND\"] = \"LAND\";\n    return PropertyType;\n}({});\nvar PriceType = /*#__PURE__*/ function(PriceType) {\n    PriceType[\"MONTH\"] = \"MONTH\";\n    PriceType[\"YEAR\"] = \"YEAR\";\n    PriceType[\"ONETIME\"] = \"ONETIME\";\n    return PriceType;\n}({});\n// Mock user database for development\nconst users = [\n    {\n        id: \"user-1\",\n        name: \"Regular User\",\n        email: \"<EMAIL>\",\n        password: \"$2b$10$8r0qPVaJIIiXxEW/Xp5kEeUvZLFdtZeK6rEjRFIgPzlGbwFE1IpnK\",\n        role: \"USER\",\n        status: \"active\",\n        emailVerified: new Date(),\n        image: null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    },\n    {\n        id: \"agent-1\",\n        name: \"Agent User\",\n        email: \"<EMAIL>\",\n        password: \"$2b$10$8r0qPVaJIIiXxEW/Xp5kEeUvZLFdtZeK6rEjRFIgPzlGbwFE1IpnK\",\n        role: \"AGENT\",\n        status: \"active\",\n        emailVerified: new Date(),\n        image: null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    },\n    {\n        id: \"landlord-1\",\n        name: \"Landlord User\",\n        email: \"<EMAIL>\",\n        password: \"$2b$10$8r0qPVaJIIiXxEW/Xp5kEeUvZLFdtZeK6rEjRFIgPzlGbwFE1IpnK\",\n        role: \"LANDLORD\",\n        status: \"active\",\n        emailVerified: new Date(),\n        image: null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    },\n    {\n        id: \"admin-1\",\n        name: \"Admin User\",\n        email: \"<EMAIL>\",\n        password: \"$2b$10$8r0qPVaJIIiXxEW/Xp5kEeUvZLFdtZeK6rEjRFIgPzlGbwFE1IpnK\",\n        role: \"ADMIN\",\n        status: \"active\",\n        emailVerified: new Date(),\n        image: null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    }\n];\n// Mock functions to simulate database operations\nasync function mockGetUserByEmail(email) {\n    return users.find((user)=>user.email === email) || null;\n}\nasync function mockGetUserById(id) {\n    return users.find((user)=>user.id === id) || null;\n}\nasync function mockCreateUser(data) {\n    const newUser = {\n        id: `user-${users.length + 1}`,\n        name: data.name || null,\n        email: data.email,\n        password: data.password || null,\n        role: data.role || \"USER\",\n        status: \"active\",\n        emailVerified: null,\n        image: data.image || null,\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n    users.push(newUser);\n    return newUser;\n}\n// Export the mock users for testing\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mock-db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\n// PrismaClient is attached to the `global` object in development to prevent\n// exhausting your database connection limit.\n// Learn more: https://pris.ly/d/help/next-js-best-practices\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLDRFQUE0RTtBQUM1RSw2Q0FBNkM7QUFDN0MsNERBQTREO0FBRTVELE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FDWEYsZ0JBQWdCRSxNQUFNLElBQ3RCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUtDLEtBQXNDLEdBQUc7UUFBQztRQUFTO1FBQVM7S0FBTyxHQUFHLENBQVM7QUFDdEYsR0FBRztBQUVMLElBQUlBLElBQXFDLEVBQUVKLGdCQUFnQkUsTUFBTSxHQUFHQTtBQUVwRSxpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFJlbnRlck5nXFxyZW50ZXJzbmctYXBwXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuLy8gUHJpc21hQ2xpZW50IGlzIGF0dGFjaGVkIHRvIHRoZSBgZ2xvYmFsYCBvYmplY3QgaW4gZGV2ZWxvcG1lbnQgdG8gcHJldmVudFxuLy8gZXhoYXVzdGluZyB5b3VyIGRhdGFiYXNlIGNvbm5lY3Rpb24gbGltaXQuXG4vLyBMZWFybiBtb3JlOiBodHRwczovL3ByaXMubHkvZC9oZWxwL25leHQtanMtYmVzdC1wcmFjdGljZXNcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsIGFzIHVua25vd24gYXMgeyBwcmlzbWE6IFByaXNtYUNsaWVudCB9O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID1cbiAgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSB8fFxuICBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXG4gIH0pO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbCIsInByaXNtYSIsImxvZyIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/schemas/auth.ts":
/*!*****************************!*\
  !*** ./src/schemas/auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoginSchema: () => (/* binding */ LoginSchema),\n/* harmony export */   NewPasswordSchema: () => (/* binding */ NewPasswordSchema),\n/* harmony export */   RegisterSchema: () => (/* binding */ RegisterSchema),\n/* harmony export */   ResetSchema: () => (/* binding */ ResetSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n\nconst LoginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email({\n        message: \"Email is required\"\n    }),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, {\n        message: \"Password is required\"\n    })\n});\nconst RegisterSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email({\n        message: \"Email is required\"\n    }),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(6, {\n        message: \"Minimum 6 characters required\"\n    }),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, {\n        message: \"Name is required\"\n    }),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        \"USER\",\n        \"AGENT\",\n        \"LANDLORD\"\n    ]).optional()\n});\nconst ResetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email({\n        message: \"Email is required\"\n    })\n});\nconst NewPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(6, {\n        message: \"Minimum of 6 characters required\"\n    })\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/schemas/auth.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("bcrypt");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproperties%2Froute&page=%2Fapi%2Fproperties%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproperties%2Froute.ts&appDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUSER%5CDocuments%5Caugment-projects%5CRenterNg%5Crentersng-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();