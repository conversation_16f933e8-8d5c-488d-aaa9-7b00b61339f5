/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/progress-stream";
exports.ids = ["vendor-chunks/progress-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/progress-stream/index.js":
/*!***********************************************!*\
  !*** ./node_modules/progress-stream/index.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var through = __webpack_require__(/*! through2 */ \"(ssr)/./node_modules/through2/through2.js\");\nvar speedometer = __webpack_require__(/*! speedometer */ \"(ssr)/./node_modules/speedometer/index.js\");\n\nmodule.exports = function(options, onprogress) {\n\tif (typeof options === 'function') return module.exports(null, options);\n\toptions = options || {};\n\n\tvar length = options.length || 0;\n\tvar time = options.time || 0;\n\tvar drain = options.drain || false;\n\tvar transferred = options.transferred || 0;\n\tvar nextUpdate = Date.now()+time;\n\tvar delta = 0;\n\tvar speed = speedometer(options.speed || 5000);\n\tvar startTime = Date.now();\n\n\tvar update = {\n\t\tpercentage: 0,\n\t\ttransferred: transferred,\n\t\tlength: length,\n\t\tremaining: length,\n\t\teta: 0,\n\t\truntime: 0\n\t};\n\n\tvar emit = function(ended) {\n\t\tupdate.delta = delta;\n\t\tupdate.percentage = ended ? 100 : (length ? transferred/length*100 : 0);\n\t\tupdate.speed = speed(delta);\n\t\tupdate.eta = Math.round(update.remaining / update.speed);\n\t\tupdate.runtime = parseInt((Date.now() - startTime)/1000);\n\t\tnextUpdate = Date.now()+time;\n\n\t\tdelta = 0;\n\n\t\ttr.emit('progress', update);\n\t};\n\tvar write = function(chunk, enc, callback) {\n\t\tvar len = options.objectMode ? 1 : chunk.length;\n\t\ttransferred += len;\n\t\tdelta += len;\n\t\tupdate.transferred = transferred;\n\t\tupdate.remaining = length >= transferred ? length - transferred : 0;\n\n\t\tif (Date.now() >= nextUpdate) emit(false);\n\t\tcallback(null, chunk);\n\t};\n\tvar end = function(callback) {\n\t\temit(true);\n\t\tcallback();\n\t};\n\n\tvar tr = through(options.objectMode ? {objectMode:true, highWaterMark:16} : {}, write, end);\n\tvar onlength = function(newLength) {\n\t\tlength = newLength;\n\t\tupdate.length = length;\n\t\tupdate.remaining = length - update.transferred;\n\t\ttr.emit('length', length);\n\t};\n\t\n\t// Expose `onlength()` handler as `setLength()` to support custom use cases where length\n\t// is not known until after a few chunks have already been pumped, or is\n\t// calculated on the fly.\n\ttr.setLength = onlength;\n\t\n\ttr.on('pipe', function(stream) {\n\t\tif (typeof length === 'number') return;\n\t\t// Support http module\n\t\tif (stream.readable && !stream.writable && stream.headers) {\n\t\t\treturn onlength(parseInt(stream.headers['content-length'] || 0));\n\t\t}\n\n\t\t// Support streams with a length property\n\t\tif (typeof stream.length === 'number') {\n\t\t\treturn onlength(stream.length);\n\t\t}\n\n\t\t// Support request module\n\t\tstream.on('response', function(res) {\n\t\t\tif (!res || !res.headers) return;\n\t\t\tif (res.headers['content-encoding'] === 'gzip') return;\n\t\t\tif (res.headers['content-length']) {\n\t\t\t\treturn onlength(parseInt(res.headers['content-length']));\n\t\t\t}\n\t\t});\n\t});\n\n\tif (drain) tr.resume();\n\tif (onprogress) tr.on('progress', onprogress);\n\n\ttr.progress = function() {\n\t\tupdate.speed = speed(0);\n\t\tupdate.eta = Math.round(update.remaining / update.speed);\n\n\t\treturn update;\n\t};\n\treturn tr;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/progress-stream/index.js\n");

/***/ })

};
;