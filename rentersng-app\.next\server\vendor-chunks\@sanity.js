/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@sanity";
exports.ids = ["vendor-chunks/@sanity"];
exports.modules = {

/***/ "(ssr)/./node_modules/@sanity/client/dist/_chunks-es/config.js":
/*!***************************************************************!*\
  !*** ./node_modules/@sanity/client/dist/_chunks-es/config.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataset: () => (/* binding */ dataset),\n/* harmony export */   defaultConfig: () => (/* binding */ defaultConfig),\n/* harmony export */   hasDataset: () => (/* binding */ hasDataset),\n/* harmony export */   initConfig: () => (/* binding */ initConfig),\n/* harmony export */   printCdnPreviewDraftsWarning: () => (/* binding */ printCdnPreviewDraftsWarning),\n/* harmony export */   printNoDefaultExport: () => (/* binding */ printNoDefaultExport),\n/* harmony export */   printPreviewDraftsDeprecationWarning: () => (/* binding */ printPreviewDraftsDeprecationWarning),\n/* harmony export */   requestTag: () => (/* binding */ requestTag),\n/* harmony export */   requireDocumentId: () => (/* binding */ requireDocumentId),\n/* harmony export */   resourceConfig: () => (/* binding */ resourceConfig),\n/* harmony export */   resourceGuard: () => (/* binding */ resourceGuard),\n/* harmony export */   validateApiPerspective: () => (/* binding */ validateApiPerspective),\n/* harmony export */   validateAssetType: () => (/* binding */ validateAssetType),\n/* harmony export */   validateDocumentId: () => (/* binding */ validateDocumentId),\n/* harmony export */   validateInsert: () => (/* binding */ validateInsert),\n/* harmony export */   validateObject: () => (/* binding */ validateObject)\n/* harmony export */ });\nconst BASE_URL = \"https://www.sanity.io/help/\";\nfunction generateHelpUrl(slug) {\n  return BASE_URL + slug;\n}\nconst VALID_ASSET_TYPES = [\"image\", \"file\"], VALID_INSERT_LOCATIONS = [\"before\", \"after\", \"replace\"], dataset = (name) => {\n  if (!/^(~[a-z0-9]{1}[-\\w]{0,63}|[a-z0-9]{1}[-\\w]{0,63})$/.test(name))\n    throw new Error(\n      \"Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters\"\n    );\n}, projectId = (id) => {\n  if (!/^[-a-z0-9]+$/i.test(id))\n    throw new Error(\"`projectId` can only contain only a-z, 0-9 and dashes\");\n}, validateAssetType = (type) => {\n  if (VALID_ASSET_TYPES.indexOf(type) === -1)\n    throw new Error(`Invalid asset type: ${type}. Must be one of ${VALID_ASSET_TYPES.join(\", \")}`);\n}, validateObject = (op, val) => {\n  if (val === null || typeof val != \"object\" || Array.isArray(val))\n    throw new Error(`${op}() takes an object of properties`);\n}, validateDocumentId = (op, id) => {\n  if (typeof id != \"string\" || !/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(id) || id.includes(\"..\"))\n    throw new Error(`${op}(): \"${id}\" is not a valid document ID`);\n}, requireDocumentId = (op, doc) => {\n  if (!doc._id)\n    throw new Error(`${op}() requires that the document contains an ID (\"_id\" property)`);\n  validateDocumentId(op, doc._id);\n}, validateInsert = (at, selector, items) => {\n  const signature = \"insert(at, selector, items)\";\n  if (VALID_INSERT_LOCATIONS.indexOf(at) === -1) {\n    const valid = VALID_INSERT_LOCATIONS.map((loc) => `\"${loc}\"`).join(\", \");\n    throw new Error(`${signature} takes an \"at\"-argument which is one of: ${valid}`);\n  }\n  if (typeof selector != \"string\")\n    throw new Error(`${signature} takes a \"selector\"-argument which must be a string`);\n  if (!Array.isArray(items))\n    throw new Error(`${signature} takes an \"items\"-argument which must be an array`);\n}, hasDataset = (config) => {\n  if (!config.dataset)\n    throw new Error(\"`dataset` must be provided to perform queries\");\n  return config.dataset || \"\";\n}, requestTag = (tag) => {\n  if (typeof tag != \"string\" || !/^[a-z0-9._-]{1,75}$/i.test(tag))\n    throw new Error(\n      \"Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.\"\n    );\n  return tag;\n}, resourceConfig = (config) => {\n  if (!config[\"~experimental_resource\"])\n    throw new Error(\"`resource` must be provided to perform resource queries\");\n  const { type, id } = config[\"~experimental_resource\"];\n  switch (type) {\n    case \"dataset\": {\n      if (id.split(\".\").length !== 2)\n        throw new Error('Dataset resource ID must be in the format \"project.dataset\"');\n      return;\n    }\n    case \"dashboard\":\n    case \"media-library\":\n    case \"canvas\":\n      return;\n    default:\n      throw new Error(`Unsupported resource type: ${type.toString()}`);\n  }\n}, resourceGuard = (service, config) => {\n  if (config[\"~experimental_resource\"])\n    throw new Error(`\\`${service}\\` does not support resource-based operations`);\n};\nfunction once(fn) {\n  let didCall = !1, returnValue;\n  return (...args) => (didCall || (returnValue = fn(...args), didCall = !0), returnValue);\n}\nconst createWarningPrinter = (message) => (\n  // eslint-disable-next-line no-console\n  once((...args) => console.warn(message.join(\" \"), ...args))\n), printCdnAndWithCredentialsWarning = createWarningPrinter([\n  \"Because you set `withCredentials` to true, we will override your `useCdn`\",\n  \"setting to be false since (cookie-based) credentials are never set on the CDN\"\n]), printCdnWarning = createWarningPrinter([\n  \"Since you haven't set a value for `useCdn`, we will deliver content using our\",\n  \"global, edge-cached API-CDN. If you wish to have content delivered faster, set\",\n  \"`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API.\"\n]), printCdnPreviewDraftsWarning = createWarningPrinter([\n  \"The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.\",\n  \"The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning.\"\n]), printPreviewDraftsDeprecationWarning = createWarningPrinter([\n  \"The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version\"\n]), printBrowserTokenWarning = createWarningPrinter([\n  \"You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.\",\n  `See ${generateHelpUrl(\n    \"js-client-browser-token\"\n  )} for more information and how to hide this warning.`\n]), printCredentialedTokenWarning = createWarningPrinter([\n  \"You have configured Sanity client to use a token, but also provided `withCredentials: true`.\",\n  \"This is no longer supported - only token will be used - remove `withCredentials: true`.\"\n]), printNoApiVersionSpecifiedWarning = createWarningPrinter([\n  \"Using the Sanity client without specifying an API version is deprecated.\",\n  `See ${generateHelpUrl(\"js-client-api-version\")}`\n]), printNoDefaultExport = createWarningPrinter([\n  \"The default export of @sanity/client has been deprecated. Use the named export `createClient` instead.\"\n]), defaultCdnHost = \"apicdn.sanity.io\", defaultConfig = {\n  apiHost: \"https://api.sanity.io\",\n  apiVersion: \"1\",\n  useProjectHostname: !0,\n  stega: { enabled: !1 }\n}, LOCALHOSTS = [\"localhost\", \"127.0.0.1\", \"0.0.0.0\"], isLocal = (host) => LOCALHOSTS.indexOf(host) !== -1;\nfunction validateApiVersion(apiVersion) {\n  if (apiVersion === \"1\" || apiVersion === \"X\")\n    return;\n  const apiDate = new Date(apiVersion);\n  if (!(/^\\d{4}-\\d{2}-\\d{2}$/.test(apiVersion) && apiDate instanceof Date && apiDate.getTime() > 0))\n    throw new Error(\"Invalid API version string, expected `1` or date in format `YYYY-MM-DD`\");\n}\nfunction validateApiPerspective(perspective) {\n  if (Array.isArray(perspective) && perspective.length > 1 && perspective.includes(\"raw\"))\n    throw new TypeError(\n      'Invalid API perspective value: \"raw\". The raw-perspective can not be combined with other perspectives'\n    );\n}\nconst initConfig = (config, prevConfig) => {\n  const specifiedConfig = {\n    ...prevConfig,\n    ...config,\n    stega: {\n      ...typeof prevConfig.stega == \"boolean\" ? { enabled: prevConfig.stega } : prevConfig.stega || defaultConfig.stega,\n      ...typeof config.stega == \"boolean\" ? { enabled: config.stega } : config.stega || {}\n    }\n  };\n  specifiedConfig.apiVersion || printNoApiVersionSpecifiedWarning();\n  const newConfig = {\n    ...defaultConfig,\n    ...specifiedConfig\n  }, projectBased = newConfig.useProjectHostname && !newConfig[\"~experimental_resource\"];\n  if (typeof Promise > \"u\") {\n    const helpUrl = generateHelpUrl(\"js-client-promise-polyfill\");\n    throw new Error(`No native Promise-implementation found, polyfill needed - see ${helpUrl}`);\n  }\n  if (projectBased && !newConfig.projectId)\n    throw new Error(\"Configuration must contain `projectId`\");\n  if (newConfig[\"~experimental_resource\"] && resourceConfig(newConfig), typeof newConfig.perspective < \"u\" && validateApiPerspective(newConfig.perspective), \"encodeSourceMap\" in newConfig)\n    throw new Error(\n      \"It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?\"\n    );\n  if (\"encodeSourceMapAtPath\" in newConfig)\n    throw new Error(\n      \"It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?\"\n    );\n  if (typeof newConfig.stega.enabled != \"boolean\")\n    throw new Error(`stega.enabled must be a boolean, received ${newConfig.stega.enabled}`);\n  if (newConfig.stega.enabled && newConfig.stega.studioUrl === void 0)\n    throw new Error(\"stega.studioUrl must be defined when stega.enabled is true\");\n  if (newConfig.stega.enabled && typeof newConfig.stega.studioUrl != \"string\" && typeof newConfig.stega.studioUrl != \"function\")\n    throw new Error(\n      `stega.studioUrl must be a string or a function, received ${newConfig.stega.studioUrl}`\n    );\n  const isBrowser = typeof window < \"u\" && window.location && window.location.hostname, isLocalhost = isBrowser && isLocal(window.location.hostname), hasToken = !!newConfig.token;\n  newConfig.withCredentials && hasToken && (printCredentialedTokenWarning(), newConfig.withCredentials = !1), isBrowser && isLocalhost && hasToken && newConfig.ignoreBrowserTokenWarning !== !0 ? printBrowserTokenWarning() : typeof newConfig.useCdn > \"u\" && printCdnWarning(), projectBased && projectId(newConfig.projectId), newConfig.dataset && dataset(newConfig.dataset), \"requestTagPrefix\" in newConfig && (newConfig.requestTagPrefix = newConfig.requestTagPrefix ? requestTag(newConfig.requestTagPrefix).replace(/\\.+$/, \"\") : void 0), newConfig.apiVersion = `${newConfig.apiVersion}`.replace(/^v/, \"\"), newConfig.isDefaultApi = newConfig.apiHost === defaultConfig.apiHost, newConfig.useCdn === !0 && newConfig.withCredentials && printCdnAndWithCredentialsWarning(), newConfig.useCdn = newConfig.useCdn !== !1 && !newConfig.withCredentials, validateApiVersion(newConfig.apiVersion);\n  const hostParts = newConfig.apiHost.split(\"://\", 2), protocol = hostParts[0], host = hostParts[1], cdnHost = newConfig.isDefaultApi ? defaultCdnHost : host;\n  return projectBased ? (newConfig.url = `${protocol}://${newConfig.projectId}.${host}/v${newConfig.apiVersion}`, newConfig.cdnUrl = `${protocol}://${newConfig.projectId}.${cdnHost}/v${newConfig.apiVersion}`) : (newConfig.url = `${newConfig.apiHost}/v${newConfig.apiVersion}`, newConfig.cdnUrl = newConfig.url), newConfig;\n};\n\n//# sourceMappingURL=config.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/client/dist/_chunks-es/config.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaClean.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@sanity/client/dist/_chunks-es/stegaClean.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   C: () => (/* binding */ C),\n/* harmony export */   stegaClean: () => (/* binding */ stegaClean),\n/* harmony export */   vercelStegaCleanAll: () => (/* binding */ vercelStegaCleanAll)\n/* harmony export */ });\nvar s = { 0: 8203, 1: 8204, 2: 8205, 3: 8290, 4: 8291, 5: 8288, 6: 65279, 7: 8289, 8: 119155, 9: 119156, a: 119157, b: 119158, c: 119159, d: 119160, e: 119161, f: 119162 }, c = { 0: 8203, 1: 8204, 2: 8205, 3: 65279 }, u = new Array(4).fill(String.fromCodePoint(c[0])).join(\"\");\nfunction E(t) {\n  let e = JSON.stringify(t);\n  return `${u}${Array.from(e).map((r) => {\n    let n = r.charCodeAt(0);\n    if (n > 255) throw new Error(`Only ASCII edit info can be encoded. Error attempting to encode ${e} on character ${r} (${n})`);\n    return Array.from(n.toString(4).padStart(4, \"0\")).map((o) => String.fromCodePoint(c[o])).join(\"\");\n  }).join(\"\")}`;\n}\nfunction I(t) {\n  return !Number.isNaN(Number(t)) || /[a-z]/i.test(t) && !/\\d+(?:[-:\\/]\\d+){2}(?:T\\d+(?:[-:\\/]\\d+){1,2}(\\.\\d+)?Z?)?/.test(t) ? !1 : !!Date.parse(t);\n}\nfunction T(t) {\n  try {\n    new URL(t, t.startsWith(\"/\") ? \"https://acme.com\" : void 0);\n  } catch {\n    return !1;\n  }\n  return !0;\n}\nfunction C(t, e, r = \"auto\") {\n  return r === !0 || r === \"auto\" && (I(t) || T(t)) ? t : `${t}${E(e)}`;\n}\nObject.fromEntries(Object.entries(c).map((t) => t.reverse()));\nObject.fromEntries(Object.entries(s).map((t) => t.reverse()));\nvar S = `${Object.values(s).map((t) => `\\\\u{${t.toString(16)}}`).join(\"\")}`, f = new RegExp(`[${S}]{4,}`, \"gu\");\nfunction _(t) {\n  var e;\n  return { cleaned: t.replace(f, \"\"), encoded: ((e = t.match(f)) == null ? void 0 : e[0]) || \"\" };\n}\nfunction O(t) {\n  return t && JSON.parse(_(JSON.stringify(t)).cleaned);\n}\nfunction stegaClean(result) {\n  return O(result);\n}\nconst vercelStegaCleanAll = stegaClean;\n\n//# sourceMappingURL=stegaClean.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9jbGllbnQvZGlzdC9fY2h1bmtzLWVzL3N0ZWdhQ2xlYW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsVUFBVSxpS0FBaUssUUFBUSxxQ0FBcUM7QUFDeE47QUFDQTtBQUNBLFlBQVksRUFBRSxFQUFFO0FBQ2hCO0FBQ0Esb0dBQW9HLEdBQUcsZUFBZSxHQUFHLEdBQUcsRUFBRTtBQUM5SDtBQUNBLEdBQUcsV0FBVztBQUNkO0FBQ0E7QUFDQSw0RUFBNEUsRUFBRSxxQkFBcUIsSUFBSTtBQUN2RztBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELEVBQUUsRUFBRSxLQUFLO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBLFdBQVcsaUNBQWlDLEVBQUUsZ0JBQWdCLFlBQVksc0JBQXNCLEVBQUUsRUFBRSxHQUFHO0FBQ3ZHO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxSZW50ZXJOZ1xccmVudGVyc25nLWFwcFxcbm9kZV9tb2R1bGVzXFxAc2FuaXR5XFxjbGllbnRcXGRpc3RcXF9jaHVua3MtZXNcXHN0ZWdhQ2xlYW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHMgPSB7IDA6IDgyMDMsIDE6IDgyMDQsIDI6IDgyMDUsIDM6IDgyOTAsIDQ6IDgyOTEsIDU6IDgyODgsIDY6IDY1Mjc5LCA3OiA4Mjg5LCA4OiAxMTkxNTUsIDk6IDExOTE1NiwgYTogMTE5MTU3LCBiOiAxMTkxNTgsIGM6IDExOTE1OSwgZDogMTE5MTYwLCBlOiAxMTkxNjEsIGY6IDExOTE2MiB9LCBjID0geyAwOiA4MjAzLCAxOiA4MjA0LCAyOiA4MjA1LCAzOiA2NTI3OSB9LCB1ID0gbmV3IEFycmF5KDQpLmZpbGwoU3RyaW5nLmZyb21Db2RlUG9pbnQoY1swXSkpLmpvaW4oXCJcIik7XG5mdW5jdGlvbiBFKHQpIHtcbiAgbGV0IGUgPSBKU09OLnN0cmluZ2lmeSh0KTtcbiAgcmV0dXJuIGAke3V9JHtBcnJheS5mcm9tKGUpLm1hcCgocikgPT4ge1xuICAgIGxldCBuID0gci5jaGFyQ29kZUF0KDApO1xuICAgIGlmIChuID4gMjU1KSB0aHJvdyBuZXcgRXJyb3IoYE9ubHkgQVNDSUkgZWRpdCBpbmZvIGNhbiBiZSBlbmNvZGVkLiBFcnJvciBhdHRlbXB0aW5nIHRvIGVuY29kZSAke2V9IG9uIGNoYXJhY3RlciAke3J9ICgke259KWApO1xuICAgIHJldHVybiBBcnJheS5mcm9tKG4udG9TdHJpbmcoNCkucGFkU3RhcnQoNCwgXCIwXCIpKS5tYXAoKG8pID0+IFN0cmluZy5mcm9tQ29kZVBvaW50KGNbb10pKS5qb2luKFwiXCIpO1xuICB9KS5qb2luKFwiXCIpfWA7XG59XG5mdW5jdGlvbiBJKHQpIHtcbiAgcmV0dXJuICFOdW1iZXIuaXNOYU4oTnVtYmVyKHQpKSB8fCAvW2Etel0vaS50ZXN0KHQpICYmICEvXFxkKyg/OlstOlxcL11cXGQrKXsyfSg/OlRcXGQrKD86Wy06XFwvXVxcZCspezEsMn0oXFwuXFxkKyk/Wj8pPy8udGVzdCh0KSA/ICExIDogISFEYXRlLnBhcnNlKHQpO1xufVxuZnVuY3Rpb24gVCh0KSB7XG4gIHRyeSB7XG4gICAgbmV3IFVSTCh0LCB0LnN0YXJ0c1dpdGgoXCIvXCIpID8gXCJodHRwczovL2FjbWUuY29tXCIgOiB2b2lkIDApO1xuICB9IGNhdGNoIHtcbiAgICByZXR1cm4gITE7XG4gIH1cbiAgcmV0dXJuICEwO1xufVxuZnVuY3Rpb24gQyh0LCBlLCByID0gXCJhdXRvXCIpIHtcbiAgcmV0dXJuIHIgPT09ICEwIHx8IHIgPT09IFwiYXV0b1wiICYmIChJKHQpIHx8IFQodCkpID8gdCA6IGAke3R9JHtFKGUpfWA7XG59XG5PYmplY3QuZnJvbUVudHJpZXMoT2JqZWN0LmVudHJpZXMoYykubWFwKCh0KSA9PiB0LnJldmVyc2UoKSkpO1xuT2JqZWN0LmZyb21FbnRyaWVzKE9iamVjdC5lbnRyaWVzKHMpLm1hcCgodCkgPT4gdC5yZXZlcnNlKCkpKTtcbnZhciBTID0gYCR7T2JqZWN0LnZhbHVlcyhzKS5tYXAoKHQpID0+IGBcXFxcdXske3QudG9TdHJpbmcoMTYpfX1gKS5qb2luKFwiXCIpfWAsIGYgPSBuZXcgUmVnRXhwKGBbJHtTfV17NCx9YCwgXCJndVwiKTtcbmZ1bmN0aW9uIF8odCkge1xuICB2YXIgZTtcbiAgcmV0dXJuIHsgY2xlYW5lZDogdC5yZXBsYWNlKGYsIFwiXCIpLCBlbmNvZGVkOiAoKGUgPSB0Lm1hdGNoKGYpKSA9PSBudWxsID8gdm9pZCAwIDogZVswXSkgfHwgXCJcIiB9O1xufVxuZnVuY3Rpb24gTyh0KSB7XG4gIHJldHVybiB0ICYmIEpTT04ucGFyc2UoXyhKU09OLnN0cmluZ2lmeSh0KSkuY2xlYW5lZCk7XG59XG5mdW5jdGlvbiBzdGVnYUNsZWFuKHJlc3VsdCkge1xuICByZXR1cm4gTyhyZXN1bHQpO1xufVxuY29uc3QgdmVyY2VsU3RlZ2FDbGVhbkFsbCA9IHN0ZWdhQ2xlYW47XG5leHBvcnQge1xuICBDLFxuICBzdGVnYUNsZWFuLFxuICB2ZXJjZWxTdGVnYUNsZWFuQWxsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RlZ2FDbGVhbi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaClean.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeIntoResult: () => (/* binding */ encodeIntoResult),\n/* harmony export */   stegaEncodeSourceMap: () => (/* binding */ stegaEncodeSourceMap),\n/* harmony export */   stegaEncodeSourceMap$1: () => (/* binding */ stegaEncodeSourceMap$1)\n/* harmony export */ });\n/* harmony import */ var _stegaClean_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stegaClean.js */ \"(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaClean.js\");\n\nconst reKeySegment = /_key\\s*==\\s*['\"](.*)['\"]/;\nfunction isKeySegment(segment) {\n  return typeof segment == \"string\" ? reKeySegment.test(segment.trim()) : typeof segment == \"object\" && \"_key\" in segment;\n}\nfunction toString(path) {\n  if (!Array.isArray(path))\n    throw new Error(\"Path is not an array\");\n  return path.reduce((target, segment, i) => {\n    const segmentType = typeof segment;\n    if (segmentType === \"number\")\n      return `${target}[${segment}]`;\n    if (segmentType === \"string\")\n      return `${target}${i === 0 ? \"\" : \".\"}${segment}`;\n    if (isKeySegment(segment) && segment._key)\n      return `${target}[_key==\"${segment._key}\"]`;\n    if (Array.isArray(segment)) {\n      const [from, to] = segment;\n      return `${target}[${from}:${to}]`;\n    }\n    throw new Error(`Unsupported path segment \\`${JSON.stringify(segment)}\\``);\n  }, \"\");\n}\nconst ESCAPE = {\n  \"\\f\": \"\\\\f\",\n  \"\\n\": \"\\\\n\",\n  \"\\r\": \"\\\\r\",\n  \"\t\": \"\\\\t\",\n  \"'\": \"\\\\'\",\n  \"\\\\\": \"\\\\\\\\\"\n}, UNESCAPE = {\n  \"\\\\f\": \"\\f\",\n  \"\\\\n\": `\n`,\n  \"\\\\r\": \"\\r\",\n  \"\\\\t\": \"\t\",\n  \"\\\\'\": \"'\",\n  \"\\\\\\\\\": \"\\\\\"\n};\nfunction jsonPath(path) {\n  return `$${path.map((segment) => typeof segment == \"string\" ? `['${segment.replace(/[\\f\\n\\r\\t'\\\\]/g, (match) => ESCAPE[match])}']` : typeof segment == \"number\" ? `[${segment}]` : segment._key !== \"\" ? `[?(@._key=='${segment._key.replace(/['\\\\]/g, (match) => ESCAPE[match])}')]` : `[${segment._index}]`).join(\"\")}`;\n}\nfunction parseJsonPath(path) {\n  const parsed = [], parseRe = /\\['(.*?)'\\]|\\[(\\d+)\\]|\\[\\?\\(@\\._key=='(.*?)'\\)\\]/g;\n  let match;\n  for (; (match = parseRe.exec(path)) !== null; ) {\n    if (match[1] !== void 0) {\n      const key = match[1].replace(/\\\\(\\\\|f|n|r|t|')/g, (m) => UNESCAPE[m]);\n      parsed.push(key);\n      continue;\n    }\n    if (match[2] !== void 0) {\n      parsed.push(parseInt(match[2], 10));\n      continue;\n    }\n    if (match[3] !== void 0) {\n      const _key = match[3].replace(/\\\\(\\\\')/g, (m) => UNESCAPE[m]);\n      parsed.push({\n        _key,\n        _index: -1\n      });\n      continue;\n    }\n  }\n  return parsed;\n}\nfunction jsonPathToStudioPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._key !== \"\")\n      return { _key: segment._key };\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction jsonPathToMappingPath(path) {\n  return path.map((segment) => {\n    if (typeof segment == \"string\" || typeof segment == \"number\")\n      return segment;\n    if (segment._index !== -1)\n      return segment._index;\n    throw new Error(`invalid segment:${JSON.stringify(segment)}`);\n  });\n}\nfunction resolveMapping(resultPath, csm) {\n  if (!csm?.mappings)\n    return;\n  const resultMappingPath = jsonPath(jsonPathToMappingPath(resultPath));\n  if (csm.mappings[resultMappingPath] !== void 0)\n    return {\n      mapping: csm.mappings[resultMappingPath],\n      matchedPath: resultMappingPath,\n      pathSuffix: \"\"\n    };\n  const mappings = Object.entries(csm.mappings).filter(([key]) => resultMappingPath.startsWith(key)).sort(([key1], [key2]) => key2.length - key1.length);\n  if (mappings.length == 0)\n    return;\n  const [matchedPath, mapping] = mappings[0], pathSuffix = resultMappingPath.substring(matchedPath.length);\n  return { mapping, matchedPath, pathSuffix };\n}\nfunction isArray(value) {\n  return value !== null && Array.isArray(value);\n}\nfunction isRecord(value) {\n  return typeof value == \"object\" && value !== null;\n}\nfunction walkMap(value, mappingFn, path = []) {\n  if (isArray(value))\n    return value.map((v, idx) => {\n      if (isRecord(v)) {\n        const _key = v._key;\n        if (typeof _key == \"string\")\n          return walkMap(v, mappingFn, path.concat({ _key, _index: idx }));\n      }\n      return walkMap(v, mappingFn, path.concat(idx));\n    });\n  if (isRecord(value)) {\n    if (value._type === \"block\" || value._type === \"span\") {\n      const result = { ...value };\n      return value._type === \"block\" ? result.children = walkMap(value.children, mappingFn, path.concat(\"children\")) : value._type === \"span\" && (result.text = walkMap(value.text, mappingFn, path.concat(\"text\"))), result;\n    }\n    return Object.fromEntries(\n      Object.entries(value).map(([k, v]) => [k, walkMap(v, mappingFn, path.concat(k))])\n    );\n  }\n  return mappingFn(value, path);\n}\nfunction encodeIntoResult(result, csm, encoder) {\n  return walkMap(result, (value, path) => {\n    if (typeof value != \"string\")\n      return value;\n    const resolveMappingResult = resolveMapping(path, csm);\n    if (!resolveMappingResult)\n      return value;\n    const { mapping, matchedPath } = resolveMappingResult;\n    if (mapping.type !== \"value\" || mapping.source.type !== \"documentValue\")\n      return value;\n    const sourceDocument = csm.documents[mapping.source.document], sourcePath = csm.paths[mapping.source.path], matchPathSegments = parseJsonPath(matchedPath), fullSourceSegments = parseJsonPath(sourcePath).concat(path.slice(matchPathSegments.length));\n    return encoder({\n      sourcePath: fullSourceSegments,\n      sourceDocument,\n      resultPath: path,\n      value\n    });\n  });\n}\nconst DRAFTS_FOLDER = \"drafts\", VERSION_FOLDER = \"versions\", PATH_SEPARATOR = \".\", DRAFTS_PREFIX = `${DRAFTS_FOLDER}${PATH_SEPARATOR}`, VERSION_PREFIX = `${VERSION_FOLDER}${PATH_SEPARATOR}`;\nfunction isDraftId(id) {\n  return id.startsWith(DRAFTS_PREFIX);\n}\nfunction isVersionId(id) {\n  return id.startsWith(VERSION_PREFIX);\n}\nfunction isPublishedId(id) {\n  return !isDraftId(id) && !isVersionId(id);\n}\nfunction getVersionFromId(id) {\n  if (!isVersionId(id)) return;\n  const [_versionPrefix, versionId, ..._publishedId] = id.split(PATH_SEPARATOR);\n  return versionId;\n}\nfunction getPublishedId(id) {\n  return isVersionId(id) ? id.split(PATH_SEPARATOR).slice(2).join(PATH_SEPARATOR) : isDraftId(id) ? id.slice(DRAFTS_PREFIX.length) : id;\n}\nfunction createEditUrl(options) {\n  const {\n    baseUrl,\n    workspace: _workspace = \"default\",\n    tool: _tool = \"default\",\n    id: _id,\n    type,\n    path,\n    projectId,\n    dataset\n  } = options;\n  if (!baseUrl)\n    throw new Error(\"baseUrl is required\");\n  if (!path)\n    throw new Error(\"path is required\");\n  if (!_id)\n    throw new Error(\"id is required\");\n  if (baseUrl !== \"/\" && baseUrl.endsWith(\"/\"))\n    throw new Error(\"baseUrl must not end with a slash\");\n  const workspace = _workspace === \"default\" ? void 0 : _workspace, tool = _tool === \"default\" ? void 0 : _tool, id = getPublishedId(_id), stringifiedPath = Array.isArray(path) ? toString(jsonPathToStudioPath(path)) : path, searchParams = new URLSearchParams({\n    baseUrl,\n    id,\n    type,\n    path: stringifiedPath\n  });\n  if (workspace && searchParams.set(\"workspace\", workspace), tool && searchParams.set(\"tool\", tool), projectId && searchParams.set(\"projectId\", projectId), dataset && searchParams.set(\"dataset\", dataset), isPublishedId(_id))\n    searchParams.set(\"perspective\", \"published\");\n  else if (isVersionId(_id)) {\n    const versionId = getVersionFromId(_id);\n    searchParams.set(\"perspective\", versionId);\n  }\n  const segments = [baseUrl === \"/\" ? \"\" : baseUrl];\n  workspace && segments.push(workspace);\n  const routerParams = [\n    \"mode=presentation\",\n    `id=${id}`,\n    `type=${type}`,\n    `path=${encodeURIComponent(stringifiedPath)}`\n  ];\n  return tool && routerParams.push(`tool=${tool}`), segments.push(\"intent\", \"edit\", `${routerParams.join(\";\")}?${searchParams}`), segments.join(\"/\");\n}\nfunction resolveStudioBaseRoute(studioUrl) {\n  let baseUrl = typeof studioUrl == \"string\" ? studioUrl : studioUrl.baseUrl;\n  return baseUrl !== \"/\" && (baseUrl = baseUrl.replace(/\\/$/, \"\")), typeof studioUrl == \"string\" ? { baseUrl } : { ...studioUrl, baseUrl };\n}\nconst filterDefault = ({ sourcePath, resultPath, value }) => {\n  if (isValidDate(value) || isValidURL(value))\n    return !1;\n  const endPath = sourcePath.at(-1);\n  return !(sourcePath.at(-2) === \"slug\" && endPath === \"current\" || typeof endPath == \"string\" && (endPath.startsWith(\"_\") || endPath.endsWith(\"Id\")) || sourcePath.some(\n    (path) => path === \"meta\" || path === \"metadata\" || path === \"openGraph\" || path === \"seo\"\n  ) || hasTypeLike(sourcePath) || hasTypeLike(resultPath) || typeof endPath == \"string\" && denylist.has(endPath));\n}, denylist = /* @__PURE__ */ new Set([\n  \"color\",\n  \"colour\",\n  \"currency\",\n  \"email\",\n  \"format\",\n  \"gid\",\n  \"hex\",\n  \"href\",\n  \"hsl\",\n  \"hsla\",\n  \"icon\",\n  \"id\",\n  \"index\",\n  \"key\",\n  \"language\",\n  \"layout\",\n  \"link\",\n  \"linkAction\",\n  \"locale\",\n  \"lqip\",\n  \"page\",\n  \"path\",\n  \"ref\",\n  \"rgb\",\n  \"rgba\",\n  \"route\",\n  \"secret\",\n  \"slug\",\n  \"status\",\n  \"tag\",\n  \"template\",\n  \"theme\",\n  \"type\",\n  \"textTheme\",\n  \"unit\",\n  \"url\",\n  \"username\",\n  \"variant\",\n  \"website\"\n]);\nfunction isValidDate(dateString) {\n  return /^\\d{4}-\\d{2}-\\d{2}/.test(dateString) ? !!Date.parse(dateString) : !1;\n}\nfunction isValidURL(url) {\n  try {\n    new URL(url, url.startsWith(\"/\") ? \"https://acme.com\" : void 0);\n  } catch {\n    return !1;\n  }\n  return !0;\n}\nfunction hasTypeLike(path) {\n  return path.some((segment) => typeof segment == \"string\" && segment.match(/type/i) !== null);\n}\nconst TRUNCATE_LENGTH = 20;\nfunction stegaEncodeSourceMap(result, resultSourceMap, config) {\n  const { filter, logger, enabled } = config;\n  if (!enabled) {\n    const msg = \"config.enabled must be true, don't call this function otherwise\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  if (!resultSourceMap)\n    return logger?.error?.(\"[@sanity/client]: Missing Content Source Map from response body\", {\n      result,\n      resultSourceMap,\n      config\n    }), result;\n  if (!config.studioUrl) {\n    const msg = \"config.studioUrl must be defined\";\n    throw logger?.error?.(`[@sanity/client]: ${msg}`, { result, resultSourceMap, config }), new TypeError(msg);\n  }\n  const report = {\n    encoded: [],\n    skipped: []\n  }, resultWithStega = encodeIntoResult(\n    result,\n    resultSourceMap,\n    ({ sourcePath, sourceDocument, resultPath, value }) => {\n      if ((typeof filter == \"function\" ? filter({ sourcePath, resultPath, filterDefault, sourceDocument, value }) : filterDefault({ sourcePath, resultPath, value })) === !1)\n        return logger && report.skipped.push({\n          path: prettyPathForLogging(sourcePath),\n          value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n          length: value.length\n        }), value;\n      logger && report.encoded.push({\n        path: prettyPathForLogging(sourcePath),\n        value: `${value.slice(0, TRUNCATE_LENGTH)}${value.length > TRUNCATE_LENGTH ? \"...\" : \"\"}`,\n        length: value.length\n      });\n      const { baseUrl, workspace, tool } = resolveStudioBaseRoute(\n        typeof config.studioUrl == \"function\" ? config.studioUrl(sourceDocument) : config.studioUrl\n      );\n      if (!baseUrl) return value;\n      const { _id: id, _type: type, _projectId: projectId, _dataset: dataset } = sourceDocument;\n      return (0,_stegaClean_js__WEBPACK_IMPORTED_MODULE_0__.C)(\n        value,\n        {\n          origin: \"sanity.io\",\n          href: createEditUrl({\n            baseUrl,\n            workspace,\n            tool,\n            id,\n            type,\n            path: sourcePath,\n            ...!config.omitCrossDatasetReferenceData && { dataset, projectId }\n          })\n        },\n        // We use custom logic to determine if we should skip encoding\n        !1\n      );\n    }\n  );\n  if (logger) {\n    const isSkipping = report.skipped.length, isEncoding = report.encoded.length;\n    if ((isSkipping || isEncoding) && ((logger?.groupCollapsed || logger.log)?.(\"[@sanity/client]: Encoding source map into result\"), logger.log?.(\n      `[@sanity/client]: Paths encoded: ${report.encoded.length}, skipped: ${report.skipped.length}`\n    )), report.encoded.length > 0 && (logger?.log?.(\"[@sanity/client]: Table of encoded paths\"), (logger?.table || logger.log)?.(report.encoded)), report.skipped.length > 0) {\n      const skipped = /* @__PURE__ */ new Set();\n      for (const { path } of report.skipped)\n        skipped.add(path.replace(reKeySegment, \"0\").replace(/\\[\\d+\\]/g, \"[]\"));\n      logger?.log?.(\"[@sanity/client]: List of skipped paths\", [...skipped.values()]);\n    }\n    (isSkipping || isEncoding) && logger?.groupEnd?.();\n  }\n  return resultWithStega;\n}\nfunction prettyPathForLogging(path) {\n  return toString(jsonPathToStudioPath(path));\n}\nvar stegaEncodeSourceMap$1 = /* @__PURE__ */ Object.freeze({\n  __proto__: null,\n  stegaEncodeSourceMap\n});\n\n//# sourceMappingURL=stegaEncodeSourceMap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/client/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@sanity/client/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BasePatch: () => (/* binding */ BasePatch),\n/* harmony export */   BaseTransaction: () => (/* binding */ BaseTransaction),\n/* harmony export */   ChannelError: () => (/* binding */ ChannelError),\n/* harmony export */   ClientError: () => (/* binding */ ClientError),\n/* harmony export */   ConnectionFailedError: () => (/* binding */ ConnectionFailedError),\n/* harmony export */   CorsOriginError: () => (/* binding */ CorsOriginError),\n/* harmony export */   DisconnectError: () => (/* binding */ DisconnectError),\n/* harmony export */   MessageError: () => (/* binding */ MessageError),\n/* harmony export */   MessageParseError: () => (/* binding */ MessageParseError),\n/* harmony export */   ObservablePatch: () => (/* binding */ ObservablePatch),\n/* harmony export */   ObservableSanityClient: () => (/* binding */ ObservableSanityClient),\n/* harmony export */   ObservableTransaction: () => (/* binding */ ObservableTransaction),\n/* harmony export */   Patch: () => (/* binding */ Patch),\n/* harmony export */   SanityClient: () => (/* binding */ SanityClient),\n/* harmony export */   ServerError: () => (/* binding */ ServerError),\n/* harmony export */   Transaction: () => (/* binding */ Transaction),\n/* harmony export */   connectEventSource: () => (/* binding */ connectEventSource),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   \"default\": () => (/* binding */ deprecatedCreateClient),\n/* harmony export */   requester: () => (/* binding */ requester),\n/* harmony export */   unstable__adapter: () => (/* reexport safe */ get_it__WEBPACK_IMPORTED_MODULE_22__.a),\n/* harmony export */   unstable__environment: () => (/* reexport safe */ get_it__WEBPACK_IMPORTED_MODULE_0__.environment),\n/* harmony export */   validateApiPerspective: () => (/* reexport safe */ _chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateApiPerspective)\n/* harmony export */ });\n/* harmony import */ var get_it__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! get-it */ \"(ssr)/./node_modules/get-it/dist/index.js\");\n/* harmony import */ var get_it__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! get-it */ \"(ssr)/./node_modules/get-it/dist/_chunks-es/node-request.js\");\n/* harmony import */ var get_it_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! get-it/middleware */ \"(ssr)/./node_modules/get-it/dist/middleware.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/Observable.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/defer.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/util/isObservable.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/of.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/operators/mergeMap.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/from.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/lastValueFrom.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/operators/shareReplay.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/operators/catchError.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/concat.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/timer.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/throwError.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/operators/tap.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/operators/finalize.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/operators/share.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/merge.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/dist/esm5/internal/observable/empty.js\");\n/* harmony import */ var _chunks_es_stegaClean_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./_chunks-es/stegaClean.js */ \"(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaClean.js\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs/operators */ \"(ssr)/./node_modules/rxjs/dist/cjs/operators/index.js\");\n/* harmony import */ var _chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./_chunks-es/config.js */ \"(ssr)/./node_modules/@sanity/client/dist/_chunks-es/config.js\");\n\n\n\n\n\n\n\nclass ClientError extends Error {\n  response;\n  statusCode = 400;\n  responseBody;\n  details;\n  constructor(res) {\n    const props = extractErrorProps(res);\n    super(props.message), Object.assign(this, props);\n  }\n}\nclass ServerError extends Error {\n  response;\n  statusCode = 500;\n  responseBody;\n  details;\n  constructor(res) {\n    const props = extractErrorProps(res);\n    super(props.message), Object.assign(this, props);\n  }\n}\nfunction extractErrorProps(res) {\n  const body = res.body, props = {\n    response: res,\n    statusCode: res.statusCode,\n    responseBody: stringifyBody(body, res),\n    message: \"\",\n    details: void 0\n  };\n  if (body.error && body.message)\n    return props.message = `${body.error} - ${body.message}`, props;\n  if (isMutationError(body) || isActionError(body)) {\n    const allItems = body.error.items || [], items = allItems.slice(0, 5).map((item) => item.error?.description).filter(Boolean);\n    let itemsStr = items.length ? `:\n- ${items.join(`\n- `)}` : \"\";\n    return allItems.length > 5 && (itemsStr += `\n...and ${allItems.length - 5} more`), props.message = `${body.error.description}${itemsStr}`, props.details = body.error, props;\n  }\n  return body.error && body.error.description ? (props.message = body.error.description, props.details = body.error, props) : (props.message = body.error || body.message || httpErrorMessage(res), props);\n}\nfunction isMutationError(body) {\n  return isPlainObject(body) && isPlainObject(body.error) && body.error.type === \"mutationError\" && typeof body.error.description == \"string\";\n}\nfunction isActionError(body) {\n  return isPlainObject(body) && isPlainObject(body.error) && body.error.type === \"actionError\" && typeof body.error.description == \"string\";\n}\nfunction isPlainObject(obj) {\n  return typeof obj == \"object\" && obj !== null && !Array.isArray(obj);\n}\nfunction httpErrorMessage(res) {\n  const statusMessage = res.statusMessage ? ` ${res.statusMessage}` : \"\";\n  return `${res.method}-request to ${res.url} resulted in HTTP ${res.statusCode}${statusMessage}`;\n}\nfunction stringifyBody(body, res) {\n  return (res.headers[\"content-type\"] || \"\").toLowerCase().indexOf(\"application/json\") !== -1 ? JSON.stringify(body, null, 2) : body;\n}\nclass CorsOriginError extends Error {\n  projectId;\n  addOriginUrl;\n  constructor({ projectId }) {\n    super(\"CorsOriginError\"), this.name = \"CorsOriginError\", this.projectId = projectId;\n    const url = new URL(`https://sanity.io/manage/project/${projectId}/api`);\n    if (typeof location < \"u\") {\n      const { origin } = location;\n      url.searchParams.set(\"cors\", \"add\"), url.searchParams.set(\"origin\", origin), this.addOriginUrl = url, this.message = `The current origin is not allowed to connect to the Live Content API. Add it here: ${url}`;\n    } else\n      this.message = `The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${url}`;\n  }\n}\nconst httpError = {\n  onResponse: (res) => {\n    if (res.statusCode >= 500)\n      throw new ServerError(res);\n    if (res.statusCode >= 400)\n      throw new ClientError(res);\n    return res;\n  }\n};\nfunction printWarnings() {\n  const seen = {};\n  return {\n    onResponse: (res) => {\n      const warn = res.headers[\"x-sanity-warning\"], warnings = Array.isArray(warn) ? warn : [warn];\n      for (const msg of warnings)\n        !msg || seen[msg] || (seen[msg] = !0, console.warn(msg));\n      return res;\n    }\n  };\n}\nfunction defineHttpRequest(envMiddleware) {\n  return (0,get_it__WEBPACK_IMPORTED_MODULE_0__.getIt)([\n    (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.retry)({ shouldRetry }),\n    ...envMiddleware,\n    printWarnings(),\n    (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.jsonRequest)(),\n    (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.jsonResponse)(),\n    (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.progress)(),\n    httpError,\n    (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.observable)({ implementation: rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable })\n  ]);\n}\nfunction shouldRetry(err, attempt, options) {\n  if (options.maxRetries === 0) return !1;\n  const isSafe = options.method === \"GET\" || options.method === \"HEAD\", isQuery2 = (options.uri || options.url).startsWith(\"/data/query\"), isRetriableResponse = err.response && (err.response.statusCode === 429 || err.response.statusCode === 502 || err.response.statusCode === 503);\n  return (isSafe || isQuery2) && isRetriableResponse ? !0 : get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.retry.shouldRetry(err, attempt, options);\n}\nclass ConnectionFailedError extends Error {\n  name = \"ConnectionFailedError\";\n}\nclass DisconnectError extends Error {\n  name = \"DisconnectError\";\n  reason;\n  constructor(message, reason, options = {}) {\n    super(message, options), this.reason = reason;\n  }\n}\nclass ChannelError extends Error {\n  name = \"ChannelError\";\n  data;\n  constructor(message, data) {\n    super(message), this.data = data;\n  }\n}\nclass MessageError extends Error {\n  name = \"MessageError\";\n  data;\n  constructor(message, data, options = {}) {\n    super(message, options), this.data = data;\n  }\n}\nclass MessageParseError extends Error {\n  name = \"MessageParseError\";\n}\nconst REQUIRED_EVENTS = [\"channelError\", \"disconnect\"];\nfunction connectEventSource(initEventSource, events) {\n  return (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.defer)(() => {\n    const es = initEventSource();\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.isObservable)(es) ? es : (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(es);\n  }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_6__.mergeMap)((es) => connectWithESInstance(es, events)));\n}\nfunction connectWithESInstance(es, events) {\n  return new rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable((observer) => {\n    const emitOpen = events.includes(\"open\"), emitReconnect = events.includes(\"reconnect\");\n    function onError(evt) {\n      if (\"data\" in evt) {\n        const [parseError, event] = parseEvent(evt);\n        observer.error(\n          parseError ? new MessageParseError(\"Unable to parse EventSource error message\", { cause: event }) : new MessageError((event?.data).message, event)\n        );\n        return;\n      }\n      es.readyState === es.CLOSED ? observer.error(new ConnectionFailedError(\"EventSource connection failed\")) : emitReconnect && observer.next({ type: \"reconnect\" });\n    }\n    function onOpen() {\n      observer.next({ type: \"open\" });\n    }\n    function onMessage(message) {\n      const [parseError, event] = parseEvent(message);\n      if (parseError) {\n        observer.error(\n          new MessageParseError(\"Unable to parse EventSource message\", { cause: parseError })\n        );\n        return;\n      }\n      if (message.type === \"channelError\") {\n        observer.error(new ChannelError(extractErrorMessage(event?.data), event.data));\n        return;\n      }\n      if (message.type === \"disconnect\") {\n        observer.error(\n          new DisconnectError(\n            `Server disconnected client: ${event.data?.reason || \"unknown error\"}`\n          )\n        );\n        return;\n      }\n      observer.next({\n        type: message.type,\n        id: message.lastEventId,\n        ...event.data ? { data: event.data } : {}\n      });\n    }\n    es.addEventListener(\"error\", onError), emitOpen && es.addEventListener(\"open\", onOpen);\n    const cleanedEvents = [.../* @__PURE__ */ new Set([...REQUIRED_EVENTS, ...events])].filter((type) => type !== \"error\" && type !== \"open\" && type !== \"reconnect\");\n    return cleanedEvents.forEach((type) => es.addEventListener(type, onMessage)), () => {\n      es.removeEventListener(\"error\", onError), emitOpen && es.removeEventListener(\"open\", onOpen), cleanedEvents.forEach((type) => es.removeEventListener(type, onMessage)), es.close();\n    };\n  });\n}\nfunction parseEvent(message) {\n  try {\n    const data = typeof message.data == \"string\" && JSON.parse(message.data);\n    return [\n      null,\n      {\n        type: message.type,\n        id: message.lastEventId,\n        ...isEmptyObject(data) ? {} : { data }\n      }\n    ];\n  } catch (err) {\n    return [err, null];\n  }\n}\nfunction extractErrorMessage(err) {\n  return err.error ? err.error.description ? err.error.description : typeof err.error == \"string\" ? err.error : JSON.stringify(err.error, null, 2) : err.message || \"Unknown listener error\";\n}\nfunction isEmptyObject(data) {\n  for (const _ in data)\n    return !1;\n  return !0;\n}\nfunction getSelection(sel) {\n  if (typeof sel == \"string\")\n    return { id: sel };\n  if (Array.isArray(sel))\n    return { query: \"*[_id in $ids]\", params: { ids: sel } };\n  if (typeof sel == \"object\" && sel !== null && \"query\" in sel && typeof sel.query == \"string\")\n    return \"params\" in sel && typeof sel.params == \"object\" && sel.params !== null ? { query: sel.query, params: sel.params } : { query: sel.query };\n  const selectionOpts = [\n    \"* Document ID (<docId>)\",\n    \"* Array of document IDs\",\n    \"* Object containing `query`\"\n  ].join(`\n`);\n  throw new Error(`Unknown selection - must be one of:\n\n${selectionOpts}`);\n}\nclass BasePatch {\n  selection;\n  operations;\n  constructor(selection, operations = {}) {\n    this.selection = selection, this.operations = operations;\n  }\n  /**\n   * Sets the given attributes to the document. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  set(attrs) {\n    return this._assign(\"set\", attrs);\n  }\n  /**\n   * Sets the given attributes to the document if they are not currently set. Does NOT merge objects.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to set. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"value\"\\}\n   */\n  setIfMissing(attrs) {\n    return this._assign(\"setIfMissing\", attrs);\n  }\n  /**\n   * Performs a \"diff-match-patch\" operation on the string attributes provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attributes to perform operation on. To set a deep attribute, use JSONMatch, eg: \\{\"nested.prop\": \"dmp\"\\}\n   */\n  diffMatchPatch(attrs) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateObject)(\"diffMatchPatch\", attrs), this._assign(\"diffMatchPatch\", attrs);\n  }\n  /**\n   * Unsets the attribute paths provided.\n   * The operation is added to the current patch, ready to be commited by `commit()`\n   *\n   * @param attrs - Attribute paths to unset.\n   */\n  unset(attrs) {\n    if (!Array.isArray(attrs))\n      throw new Error(\"unset(attrs) takes an array of attributes to unset, non-array given\");\n    return this.operations = Object.assign({}, this.operations, { unset: attrs }), this;\n  }\n  /**\n   * Increment a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to increment, values representing the number to increment by.\n   */\n  inc(attrs) {\n    return this._assign(\"inc\", attrs);\n  }\n  /**\n   * Decrement a numeric value. Each entry in the argument is either an attribute or a JSON path. The value may be a positive or negative integer or floating-point value. The operation will fail if target value is not a numeric value, or doesn't exist.\n   *\n   * @param attrs - Object of attribute paths to decrement, values representing the number to decrement by.\n   */\n  dec(attrs) {\n    return this._assign(\"dec\", attrs);\n  }\n  /**\n   * Provides methods for modifying arrays, by inserting, appending and replacing elements via a JSONPath expression.\n   *\n   * @param at - Location to insert at, relative to the given selector, or 'replace' the matched path\n   * @param selector - JSONPath expression, eg `comments[-1]` or `blocks[_key==\"abc123\"]`\n   * @param items - Array of items to insert/replace\n   */\n  insert(at, selector, items) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateInsert)(at, selector, items), this._assign(\"insert\", { [at]: selector, items });\n  }\n  /**\n   * Append the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to append to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to append to the array\n   */\n  append(selector, items) {\n    return this.insert(\"after\", `${selector}[-1]`, items);\n  }\n  /**\n   * Prepend the given items to the array at the given JSONPath\n   *\n   * @param selector - Attribute/path to prepend to, eg `comments` or `person.hobbies`\n   * @param items - Array of items to prepend to the array\n   */\n  prepend(selector, items) {\n    return this.insert(\"before\", `${selector}[0]`, items);\n  }\n  /**\n   * Change the contents of an array by removing existing elements and/or adding new elements.\n   *\n   * @param selector - Attribute or JSONPath expression for array\n   * @param start - Index at which to start changing the array (with origin 0). If greater than the length of the array, actual starting index will be set to the length of the array. If negative, will begin that many elements from the end of the array (with origin -1) and will be set to 0 if absolute value is greater than the length of the array.x\n   * @param deleteCount - An integer indicating the number of old array elements to remove.\n   * @param items - The elements to add to the array, beginning at the start index. If you don't specify any elements, splice() will only remove elements from the array.\n   */\n  splice(selector, start, deleteCount, items) {\n    const delAll = typeof deleteCount > \"u\" || deleteCount === -1, startIndex = start < 0 ? start - 1 : start, delCount = delAll ? -1 : Math.max(0, start + deleteCount), delRange = startIndex < 0 && delCount >= 0 ? \"\" : delCount, rangeSelector = `${selector}[${startIndex}:${delRange}]`;\n    return this.insert(\"replace\", rangeSelector, items || []);\n  }\n  /**\n   * Adds a revision clause, preventing the document from being patched if the `_rev` property does not match the given value\n   *\n   * @param rev - Revision to lock the patch to\n   */\n  ifRevisionId(rev) {\n    return this.operations.ifRevisionID = rev, this;\n  }\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  serialize() {\n    return { ...getSelection(this.selection), ...this.operations };\n  }\n  /**\n   * Return a plain JSON representation of the patch\n   */\n  toJSON() {\n    return this.serialize();\n  }\n  /**\n   * Clears the patch of all operations\n   */\n  reset() {\n    return this.operations = {}, this;\n  }\n  _assign(op, props, merge2 = !0) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateObject)(op, props), this.operations = Object.assign({}, this.operations, {\n      [op]: Object.assign({}, merge2 && this.operations[op] || {}, props)\n    }), this;\n  }\n  _set(op, props) {\n    return this._assign(op, props, !1);\n  }\n}\nclass ObservablePatch extends BasePatch {\n  #client;\n  constructor(selection, operations, client) {\n    super(selection, operations), this.#client = client;\n  }\n  /**\n   * Clones the patch\n   */\n  clone() {\n    return new ObservablePatch(this.selection, { ...this.operations }, this.#client);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method\"\n      );\n    const returnFirst = typeof this.selection == \"string\", opts = Object.assign({ returnFirst, returnDocuments: !0 }, options);\n    return this.#client.mutate({ patch: this.serialize() }, opts);\n  }\n}\nclass Patch extends BasePatch {\n  #client;\n  constructor(selection, operations, client) {\n    super(selection, operations), this.#client = client;\n  }\n  /**\n   * Clones the patch\n   */\n  clone() {\n    return new Patch(this.selection, { ...this.operations }, this.#client);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method\"\n      );\n    const returnFirst = typeof this.selection == \"string\", opts = Object.assign({ returnFirst, returnDocuments: !0 }, options);\n    return this.#client.mutate({ patch: this.serialize() }, opts);\n  }\n}\nconst defaultMutateOptions = { returnDocuments: !1 };\nclass BaseTransaction {\n  operations;\n  trxId;\n  constructor(operations = [], transactionId) {\n    this.operations = operations, this.trxId = transactionId;\n  }\n  /**\n   * Creates a new Sanity document. If `_id` is provided and already exists, the mutation will fail. If no `_id` is given, one will automatically be generated by the database.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create. Requires a `_type` property.\n   */\n  create(doc) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateObject)(\"create\", doc), this._add({ create: doc });\n  }\n  /**\n   * Creates a new Sanity document. If a document with the same `_id` already exists, the create operation will be ignored.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create if it does not already exist. Requires `_id` and `_type` properties.\n   */\n  createIfNotExists(doc) {\n    const op = \"createIfNotExists\";\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateObject)(op, doc), (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.requireDocumentId)(op, doc), this._add({ [op]: doc });\n  }\n  /**\n   * Creates a new Sanity document, or replaces an existing one if the same `_id` is already used.\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param doc - Document to create or replace. Requires `_id` and `_type` properties.\n   */\n  createOrReplace(doc) {\n    const op = \"createOrReplace\";\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateObject)(op, doc), (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.requireDocumentId)(op, doc), this._add({ [op]: doc });\n  }\n  /**\n   * Deletes the document with the given document ID\n   * The operation is added to the current transaction, ready to be commited by `commit()`\n   *\n   * @param documentId - Document ID to delete\n   */\n  delete(documentId) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateDocumentId)(\"delete\", documentId), this._add({ delete: { id: documentId } });\n  }\n  transactionId(id) {\n    return id ? (this.trxId = id, this) : this.trxId;\n  }\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  serialize() {\n    return [...this.operations];\n  }\n  /**\n   * Return a plain JSON representation of the transaction\n   */\n  toJSON() {\n    return this.serialize();\n  }\n  /**\n   * Clears the transaction of all operations\n   */\n  reset() {\n    return this.operations = [], this;\n  }\n  _add(mut) {\n    return this.operations.push(mut), this;\n  }\n}\nclass Transaction extends BaseTransaction {\n  #client;\n  constructor(operations, client, transactionId) {\n    super(operations, transactionId), this.#client = client;\n  }\n  /**\n   * Clones the transaction\n   */\n  clone() {\n    return new Transaction([...this.operations], this.#client, this.trxId);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method\"\n      );\n    return this.#client.mutate(\n      this.serialize(),\n      Object.assign({ transactionId: this.trxId }, defaultMutateOptions, options || {})\n    );\n  }\n  patch(patchOrDocumentId, patchOps) {\n    const isBuilder = typeof patchOps == \"function\", isPatch = typeof patchOrDocumentId != \"string\" && patchOrDocumentId instanceof Patch, isMutationSelection = typeof patchOrDocumentId == \"object\" && (\"query\" in patchOrDocumentId || \"id\" in patchOrDocumentId);\n    if (isPatch)\n      return this._add({ patch: patchOrDocumentId.serialize() });\n    if (isBuilder) {\n      const patch = patchOps(new Patch(patchOrDocumentId, {}, this.#client));\n      if (!(patch instanceof Patch))\n        throw new Error(\"function passed to `patch()` must return the patch\");\n      return this._add({ patch: patch.serialize() });\n    }\n    if (isMutationSelection) {\n      const patch = new Patch(patchOrDocumentId, patchOps || {}, this.#client);\n      return this._add({ patch: patch.serialize() });\n    }\n    return this._add({ patch: { id: patchOrDocumentId, ...patchOps } });\n  }\n}\nclass ObservableTransaction extends BaseTransaction {\n  #client;\n  constructor(operations, client, transactionId) {\n    super(operations, transactionId), this.#client = client;\n  }\n  /**\n   * Clones the transaction\n   */\n  clone() {\n    return new ObservableTransaction([...this.operations], this.#client, this.trxId);\n  }\n  commit(options) {\n    if (!this.#client)\n      throw new Error(\n        \"No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method\"\n      );\n    return this.#client.mutate(\n      this.serialize(),\n      Object.assign({ transactionId: this.trxId }, defaultMutateOptions, options || {})\n    );\n  }\n  patch(patchOrDocumentId, patchOps) {\n    const isBuilder = typeof patchOps == \"function\";\n    if (typeof patchOrDocumentId != \"string\" && patchOrDocumentId instanceof ObservablePatch)\n      return this._add({ patch: patchOrDocumentId.serialize() });\n    if (isBuilder) {\n      const patch = patchOps(new ObservablePatch(patchOrDocumentId, {}, this.#client));\n      if (!(patch instanceof ObservablePatch))\n        throw new Error(\"function passed to `patch()` must return the patch\");\n      return this._add({ patch: patch.serialize() });\n    }\n    return this._add({ patch: { id: patchOrDocumentId, ...patchOps } });\n  }\n}\nconst projectHeader = \"X-Sanity-Project-ID\";\nfunction requestOptions(config, overrides = {}) {\n  const headers2 = {}, token = overrides.token || config.token;\n  token && (headers2.Authorization = `Bearer ${token}`), !overrides.useGlobalApi && !config.useProjectHostname && config.projectId && (headers2[projectHeader] = config.projectId);\n  const withCredentials = !!(typeof overrides.withCredentials > \"u\" ? config.withCredentials : overrides.withCredentials), timeout = typeof overrides.timeout > \"u\" ? config.timeout : overrides.timeout;\n  return Object.assign({}, overrides, {\n    headers: Object.assign({}, headers2, overrides.headers || {}),\n    timeout: typeof timeout > \"u\" ? 5 * 60 * 1e3 : timeout,\n    proxy: overrides.proxy || config.proxy,\n    json: !0,\n    withCredentials,\n    fetch: typeof overrides.fetch == \"object\" && typeof config.fetch == \"object\" ? { ...config.fetch, ...overrides.fetch } : overrides.fetch || config.fetch\n  });\n}\nconst encodeQueryString = ({\n  query,\n  params = {},\n  options = {}\n}) => {\n  const searchParams = new URLSearchParams(), { tag, includeMutations, returnQuery, ...opts } = options;\n  tag && searchParams.append(\"tag\", tag), searchParams.append(\"query\", query);\n  for (const [key, value] of Object.entries(params))\n    searchParams.append(`$${key}`, JSON.stringify(value));\n  for (const [key, value] of Object.entries(opts))\n    value && searchParams.append(key, `${value}`);\n  return returnQuery === !1 && searchParams.append(\"returnQuery\", \"false\"), includeMutations === !1 && searchParams.append(\"includeMutations\", \"false\"), `?${searchParams}`;\n}, excludeFalsey = (param, defValue) => param === !1 ? void 0 : typeof param > \"u\" ? defValue : param, getMutationQuery = (options = {}) => ({\n  dryRun: options.dryRun,\n  returnIds: !0,\n  returnDocuments: excludeFalsey(options.returnDocuments, !0),\n  visibility: options.visibility || \"sync\",\n  autoGenerateArrayKeys: options.autoGenerateArrayKeys,\n  skipCrossDatasetReferenceValidation: options.skipCrossDatasetReferenceValidation\n}), isResponse = (event) => event.type === \"response\", getBody = (event) => event.body, indexBy = (docs, attr) => docs.reduce((indexed, doc) => (indexed[attr(doc)] = doc, indexed), /* @__PURE__ */ Object.create(null)), getQuerySizeLimit = 11264;\nfunction _fetch(client, httpRequest, _stega, query, _params = {}, options = {}) {\n  const stega = \"stega\" in options ? {\n    ..._stega || {},\n    ...typeof options.stega == \"boolean\" ? { enabled: options.stega } : options.stega || {}\n  } : _stega, params = stega.enabled ? (0,_chunks_es_stegaClean_js__WEBPACK_IMPORTED_MODULE_8__.stegaClean)(_params) : _params, mapResponse = options.filterResponse === !1 ? (res) => res : (res) => res.result, { cache, next, ...opts } = {\n    // Opt out of setting a `signal` on an internal `fetch` if one isn't provided.\n    // This is necessary in React Server Components to avoid opting out of Request Memoization.\n    useAbortSignal: typeof options.signal < \"u\",\n    // Set `resultSourceMap' when stega is enabled, as it's required for encoding.\n    resultSourceMap: stega.enabled ? \"withKeyArraySelector\" : options.resultSourceMap,\n    ...options,\n    // Default to not returning the query, unless `filterResponse` is `false`,\n    // or `returnQuery` is explicitly set. `true` is the default in Content Lake, so skip if truthy\n    returnQuery: options.filterResponse === !1 && options.returnQuery !== !1\n  }, reqOpts = typeof cache < \"u\" || typeof next < \"u\" ? { ...opts, fetch: { cache, next } } : opts, $request = _dataRequest(client, httpRequest, \"query\", { query, params }, reqOpts);\n  return stega.enabled ? $request.pipe(\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.combineLatestWith)(\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.from)(\n        __webpack_require__.e(/*! import() */ \"vendor-chunks/@sanity\").then(__webpack_require__.bind(__webpack_require__, /*! ./_chunks-es/stegaEncodeSourceMap.js */ \"(ssr)/./node_modules/@sanity/client/dist/_chunks-es/stegaEncodeSourceMap.js\")).then(function(n) {\n          return n.stegaEncodeSourceMap$1;\n        }).then(\n          ({ stegaEncodeSourceMap }) => stegaEncodeSourceMap\n        )\n      )\n    ),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)(\n      ([res, stegaEncodeSourceMap]) => {\n        const result = stegaEncodeSourceMap(res.result, res.resultSourceMap, stega);\n        return mapResponse({ ...res, result });\n      }\n    )\n  ) : $request.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)(mapResponse));\n}\nfunction _getDocument(client, httpRequest, id, opts = {}) {\n  const options = {\n    uri: _getDataUrl(client, \"doc\", id),\n    json: !0,\n    tag: opts.tag,\n    signal: opts.signal\n  };\n  return _requestObservable(client, httpRequest, options).pipe(\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.filter)(isResponse),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((event) => event.body.documents && event.body.documents[0])\n  );\n}\nfunction _getDocuments(client, httpRequest, ids, opts = {}) {\n  const options = {\n    uri: _getDataUrl(client, \"doc\", ids.join(\",\")),\n    json: !0,\n    tag: opts.tag,\n    signal: opts.signal\n  };\n  return _requestObservable(client, httpRequest, options).pipe(\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.filter)(isResponse),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((event) => {\n      const indexed = indexBy(event.body.documents || [], (doc) => doc._id);\n      return ids.map((id) => indexed[id] || null);\n    })\n  );\n}\nfunction _createIfNotExists(client, httpRequest, doc, options) {\n  return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.requireDocumentId)(\"createIfNotExists\", doc), _create(client, httpRequest, doc, \"createIfNotExists\", options);\n}\nfunction _createOrReplace(client, httpRequest, doc, options) {\n  return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.requireDocumentId)(\"createOrReplace\", doc), _create(client, httpRequest, doc, \"createOrReplace\", options);\n}\nfunction _delete(client, httpRequest, selection, options) {\n  return _dataRequest(\n    client,\n    httpRequest,\n    \"mutate\",\n    { mutations: [{ delete: getSelection(selection) }] },\n    options\n  );\n}\nfunction _mutate(client, httpRequest, mutations, options) {\n  let mut;\n  mutations instanceof Patch || mutations instanceof ObservablePatch ? mut = { patch: mutations.serialize() } : mutations instanceof Transaction || mutations instanceof ObservableTransaction ? mut = mutations.serialize() : mut = mutations;\n  const muts = Array.isArray(mut) ? mut : [mut], transactionId = options && options.transactionId || void 0;\n  return _dataRequest(client, httpRequest, \"mutate\", { mutations: muts, transactionId }, options);\n}\nfunction _action(client, httpRequest, actions, options) {\n  const acts = Array.isArray(actions) ? actions : [actions], transactionId = options && options.transactionId || void 0, skipCrossDatasetReferenceValidation = options && options.skipCrossDatasetReferenceValidation || void 0, dryRun = options && options.dryRun || void 0;\n  return _dataRequest(\n    client,\n    httpRequest,\n    \"actions\",\n    { actions: acts, transactionId, skipCrossDatasetReferenceValidation, dryRun },\n    options\n  );\n}\nfunction _dataRequest(client, httpRequest, endpoint, body, options = {}) {\n  const isMutation = endpoint === \"mutate\", isAction = endpoint === \"actions\", isQuery2 = endpoint === \"query\", strQuery = isMutation || isAction ? \"\" : encodeQueryString(body), useGet = !isMutation && !isAction && strQuery.length < getQuerySizeLimit, stringQuery = useGet ? strQuery : \"\", returnFirst = options.returnFirst, { timeout, token, tag, headers: headers2, returnQuery, lastLiveEventId, cacheMode } = options, uri = _getDataUrl(client, endpoint, stringQuery), reqOptions = {\n    method: useGet ? \"GET\" : \"POST\",\n    uri,\n    json: !0,\n    body: useGet ? void 0 : body,\n    query: isMutation && getMutationQuery(options),\n    timeout,\n    headers: headers2,\n    token,\n    tag,\n    returnQuery,\n    perspective: options.perspective,\n    resultSourceMap: options.resultSourceMap,\n    lastLiveEventId: Array.isArray(lastLiveEventId) ? lastLiveEventId[0] : lastLiveEventId,\n    cacheMode,\n    canUseCdn: isQuery2,\n    signal: options.signal,\n    fetch: options.fetch,\n    useAbortSignal: options.useAbortSignal,\n    useCdn: options.useCdn\n  };\n  return _requestObservable(client, httpRequest, reqOptions).pipe(\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.filter)(isResponse),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)(getBody),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((res) => {\n      if (!isMutation)\n        return res;\n      const results = res.results || [];\n      if (options.returnDocuments)\n        return returnFirst ? results[0] && results[0].document : results.map((mut) => mut.document);\n      const key = returnFirst ? \"documentId\" : \"documentIds\", ids = returnFirst ? results[0] && results[0].id : results.map((mut) => mut.id);\n      return {\n        transactionId: res.transactionId,\n        results,\n        [key]: ids\n      };\n    })\n  );\n}\nfunction _create(client, httpRequest, doc, op, options = {}) {\n  const mutation = { [op]: doc }, opts = Object.assign({ returnFirst: !0, returnDocuments: !0 }, options);\n  return _dataRequest(client, httpRequest, \"mutate\", { mutations: [mutation] }, opts);\n}\nconst hasDataConfig = (client) => client.config().dataset !== void 0 && client.config().projectId !== void 0 || client.config()[\"~experimental_resource\"] !== void 0, isQuery = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"query\")), isMutate = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"mutate\")), isDoc = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"doc\", \"\")), isListener = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"listen\")), isHistory = (client, uri) => hasDataConfig(client) && uri.startsWith(_getDataUrl(client, \"history\", \"\")), isData = (client, uri) => uri.startsWith(\"/data/\") || isQuery(client, uri) || isMutate(client, uri) || isDoc(client, uri) || isListener(client, uri) || isHistory(client, uri);\nfunction _requestObservable(client, httpRequest, options) {\n  const uri = options.url || options.uri, config = client.config(), canUseCdn = typeof options.canUseCdn > \"u\" ? [\"GET\", \"HEAD\"].indexOf(options.method || \"GET\") >= 0 && isData(client, uri) : options.canUseCdn;\n  let useCdn = (options.useCdn ?? config.useCdn) && canUseCdn;\n  const tag = options.tag && config.requestTagPrefix ? [config.requestTagPrefix, options.tag].join(\".\") : options.tag || config.requestTagPrefix;\n  if (tag && options.tag !== null && (options.query = { tag: (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.requestTag)(tag), ...options.query }), [\"GET\", \"HEAD\", \"POST\"].indexOf(options.method || \"GET\") >= 0 && isQuery(client, uri)) {\n    const resultSourceMap = options.resultSourceMap ?? config.resultSourceMap;\n    resultSourceMap !== void 0 && resultSourceMap !== !1 && (options.query = { resultSourceMap, ...options.query });\n    const perspectiveOption = options.perspective || config.perspective;\n    typeof perspectiveOption < \"u\" && (perspectiveOption === \"previewDrafts\" && (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.printPreviewDraftsDeprecationWarning)(), (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateApiPerspective)(perspectiveOption), options.query = {\n      perspective: Array.isArray(perspectiveOption) ? perspectiveOption.join(\",\") : perspectiveOption,\n      ...options.query\n    }, (Array.isArray(perspectiveOption) && perspectiveOption.length > 0 || // previewDrafts was renamed to drafts, but keep for backwards compat\n    perspectiveOption === \"previewDrafts\" || perspectiveOption === \"drafts\") && useCdn && (useCdn = !1, (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.printCdnPreviewDraftsWarning)())), options.lastLiveEventId && (options.query = { ...options.query, lastLiveEventId: options.lastLiveEventId }), options.returnQuery === !1 && (options.query = { returnQuery: \"false\", ...options.query }), useCdn && options.cacheMode == \"noStale\" && (options.query = { cacheMode: \"noStale\", ...options.query });\n  }\n  const reqOptions = requestOptions(\n    config,\n    Object.assign({}, options, {\n      url: _getUrl(client, uri, useCdn)\n    })\n  ), request = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable(\n    (subscriber) => httpRequest(reqOptions, config.requester).subscribe(subscriber)\n  );\n  return options.signal ? request.pipe(_withAbortSignal(options.signal)) : request;\n}\nfunction _request(client, httpRequest, options) {\n  return _requestObservable(client, httpRequest, options).pipe(\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.filter)((event) => event.type === \"response\"),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((event) => event.body)\n  );\n}\nfunction _getDataUrl(client, operation, path) {\n  const config = client.config();\n  if (config[\"~experimental_resource\"]) {\n    (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceConfig)(config);\n    const resourceBase = resourceDataBase(config), uri2 = path !== void 0 ? `${operation}/${path}` : operation;\n    return `${resourceBase}/${uri2}`.replace(/\\/($|\\?)/, \"$1\");\n  }\n  const catalog = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.hasDataset)(config), baseUri = `/${operation}/${catalog}`;\n  return `/data${path !== void 0 ? `${baseUri}/${path}` : baseUri}`.replace(/\\/($|\\?)/, \"$1\");\n}\nfunction _getUrl(client, uri, canUseCdn = !1) {\n  const { url, cdnUrl } = client.config();\n  return `${canUseCdn ? cdnUrl : url}/${uri.replace(/^\\//, \"\")}`;\n}\nfunction _withAbortSignal(signal) {\n  return (input) => new rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable((observer) => {\n    const abort = () => observer.error(_createAbortError(signal));\n    if (signal && signal.aborted) {\n      abort();\n      return;\n    }\n    const subscription = input.subscribe(observer);\n    return signal.addEventListener(\"abort\", abort), () => {\n      signal.removeEventListener(\"abort\", abort), subscription.unsubscribe();\n    };\n  });\n}\nconst isDomExceptionSupported = !!globalThis.DOMException;\nfunction _createAbortError(signal) {\n  if (isDomExceptionSupported)\n    return new DOMException(signal?.reason ?? \"The operation was aborted.\", \"AbortError\");\n  const error = new Error(signal?.reason ?? \"The operation was aborted.\");\n  return error.name = \"AbortError\", error;\n}\nconst resourceDataBase = (config) => {\n  if (!config[\"~experimental_resource\"])\n    throw new Error(\"`resource` must be provided to perform resource queries\");\n  const { type, id } = config[\"~experimental_resource\"];\n  switch (type) {\n    case \"dataset\": {\n      const segments = id.split(\".\");\n      if (segments.length !== 2)\n        throw new Error('Dataset ID must be in the format \"project.dataset\"');\n      return `/projects/${segments[0]}/datasets/${segments[1]}`;\n    }\n    case \"canvas\":\n      return `/canvases/${id}`;\n    case \"media-library\":\n      return `/media-libraries/${id}`;\n    case \"dashboard\":\n      return `/dashboards/${id}`;\n    default:\n      throw new Error(`Unsupported resource type: ${type.toString()}`);\n  }\n};\nfunction _generate(client, httpRequest, request) {\n  const dataset2 = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.hasDataset)(client.config());\n  return _request(client, httpRequest, {\n    method: \"POST\",\n    uri: `/agent/action/generate/${dataset2}`,\n    body: request\n  });\n}\nfunction _transform(client, httpRequest, request) {\n  const dataset2 = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.hasDataset)(client.config());\n  return _request(client, httpRequest, {\n    method: \"POST\",\n    uri: `/agent/action/transform/${dataset2}`,\n    body: request\n  });\n}\nfunction _translate(client, httpRequest, request) {\n  const dataset2 = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.hasDataset)(client.config());\n  return _request(client, httpRequest, {\n    method: \"POST\",\n    uri: `/agent/action/translate/${dataset2}`,\n    body: request\n  });\n}\nclass ObservableAgentsActionClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Run an instruction to generate content in a target document.\n   * @param request - instruction request\n   */\n  generate(request) {\n    return _generate(this.#client, this.#httpRequest, request);\n  }\n  /**\n   * Transform a target document based on a source.\n   * @param request - translation request\n   */\n  transform(request) {\n    return _transform(this.#client, this.#httpRequest, request);\n  }\n  /**\n   * Translate a target document based on a source.\n   * @param request - translation request\n   */\n  translate(request) {\n    return _translate(this.#client, this.#httpRequest, request);\n  }\n}\nclass AgentActionsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Run an instruction to generate content in a target document.\n   * @param request - instruction request\n   */\n  generate(request) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_generate(this.#client, this.#httpRequest, request));\n  }\n  /**\n   * Transform a target document based on a source.\n   * @param request - translation request\n   */\n  transform(request) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_transform(this.#client, this.#httpRequest, request));\n  }\n  /**\n   * Translate a target document based on a source.\n   * @param request - translation request\n   */\n  translate(request) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_translate(this.#client, this.#httpRequest, request));\n  }\n}\nclass ObservableAssetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  upload(assetType, body, options) {\n    return _upload(this.#client, this.#httpRequest, assetType, body, options);\n  }\n}\nclass AssetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  upload(assetType, body, options) {\n    const observable2 = _upload(this.#client, this.#httpRequest, assetType, body, options);\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      observable2.pipe(\n        (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.filter)((event) => event.type === \"response\"),\n        (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)(\n          (event) => event.body.document\n        )\n      )\n    );\n  }\n}\nfunction _upload(client, httpRequest, assetType, body, opts = {}) {\n  (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.validateAssetType)(assetType);\n  let meta = opts.extract || void 0;\n  meta && !meta.length && (meta = [\"none\"]);\n  const config = client.config(), options = optionsFromFile(opts, body), { tag, label, title, description, creditLine, filename, source } = options, query = {\n    label,\n    title,\n    description,\n    filename,\n    meta,\n    creditLine\n  };\n  return source && (query.sourceId = source.id, query.sourceName = source.name, query.sourceUrl = source.url), _requestObservable(client, httpRequest, {\n    tag,\n    method: \"POST\",\n    timeout: options.timeout || 0,\n    uri: buildAssetUploadUrl(config, assetType),\n    headers: options.contentType ? { \"Content-Type\": options.contentType } : {},\n    query,\n    body\n  });\n}\nfunction buildAssetUploadUrl(config, assetType) {\n  const assetTypeEndpoint = assetType === \"image\" ? \"images\" : \"files\";\n  if (config[\"~experimental_resource\"]) {\n    const { type, id } = config[\"~experimental_resource\"];\n    switch (type) {\n      case \"dataset\":\n        throw new Error(\n          \"Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.\"\n        );\n      case \"canvas\":\n        return `/canvases/${id}/assets/${assetTypeEndpoint}`;\n      case \"media-library\":\n        return `/media-libraries/${id}/upload`;\n      case \"dashboard\":\n        return `/dashboards/${id}/assets/${assetTypeEndpoint}`;\n      default:\n        throw new Error(`Unsupported resource type: ${type.toString()}`);\n    }\n  }\n  const dataset2 = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.hasDataset)(config);\n  return `assets/${assetTypeEndpoint}/${dataset2}`;\n}\nfunction optionsFromFile(opts, file) {\n  return typeof File > \"u\" || !(file instanceof File) ? opts : Object.assign(\n    {\n      filename: opts.preserveFilename === !1 ? void 0 : file.name,\n      contentType: file.type\n    },\n    opts\n  );\n}\nvar defaults = (obj, defaults2) => Object.keys(defaults2).concat(Object.keys(obj)).reduce((target, prop) => (target[prop] = typeof obj[prop] > \"u\" ? defaults2[prop] : obj[prop], target), {});\nconst pick = (obj, props) => props.reduce((selection, prop) => (typeof obj[prop] > \"u\" || (selection[prop] = obj[prop]), selection), {}), eventSourcePolyfill = (0,rxjs__WEBPACK_IMPORTED_MODULE_3__.defer)(() => __webpack_require__.e(/*! import() */ \"vendor-chunks/@sanity\").then(__webpack_require__.t.bind(__webpack_require__, /*! @sanity/eventsource */ \"(ssr)/./node_modules/@sanity/eventsource/node.js\", 19))).pipe(\n  (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)(({ default: EventSource2 }) => EventSource2),\n  (0,rxjs__WEBPACK_IMPORTED_MODULE_12__.shareReplay)(1)\n);\nfunction reconnectOnConnectionFailure() {\n  return function(source) {\n    return source.pipe(\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.catchError)((err, caught) => err instanceof ConnectionFailedError ? (0,rxjs__WEBPACK_IMPORTED_MODULE_14__.concat)((0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)({ type: \"reconnect\" }), (0,rxjs__WEBPACK_IMPORTED_MODULE_15__.timer)(1e3).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_6__.mergeMap)(() => caught))) : (0,rxjs__WEBPACK_IMPORTED_MODULE_16__.throwError)(() => err))\n    );\n  };\n}\nconst MAX_URL_LENGTH = 14800, possibleOptions = [\n  \"includePreviousRevision\",\n  \"includeResult\",\n  \"includeMutations\",\n  \"includeAllVersions\",\n  \"visibility\",\n  \"effectFormat\",\n  \"tag\"\n], defaultOptions = {\n  includeResult: !0\n};\nfunction _listen(query, params, opts = {}) {\n  const { url, token, withCredentials, requestTagPrefix } = this.config(), tag = opts.tag && requestTagPrefix ? [requestTagPrefix, opts.tag].join(\".\") : opts.tag, options = { ...defaults(opts, defaultOptions), tag }, listenOpts = pick(options, possibleOptions), qs = encodeQueryString({ query, params, options: { tag, ...listenOpts } }), uri = `${url}${_getDataUrl(this, \"listen\", qs)}`;\n  if (uri.length > MAX_URL_LENGTH)\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_16__.throwError)(() => new Error(\"Query too large for listener\"));\n  const listenFor = options.events ? options.events : [\"mutation\"], esOptions = {};\n  return withCredentials && (esOptions.withCredentials = !0), token && (esOptions.headers = {\n    Authorization: `Bearer ${token}`\n  }), connectEventSource(() => (\n    // use polyfill if there is no global EventSource or if we need to set headers\n    (typeof EventSource > \"u\" || esOptions.headers ? eventSourcePolyfill : (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(EventSource)).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((EventSource2) => new EventSource2(uri, esOptions)))\n  ), listenFor).pipe(\n    reconnectOnConnectionFailure(),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.filter)((event) => listenFor.includes(event.type)),\n    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)(\n      (event) => ({\n        type: event.type,\n        ...\"data\" in event ? event.data : {}\n      })\n    )\n  );\n}\nfunction shareReplayLatest(configOrPredicate, config) {\n  return _shareReplayLatest(\n    typeof configOrPredicate == \"function\" ? { predicate: configOrPredicate, ...config } : configOrPredicate\n  );\n}\nfunction _shareReplayLatest(config) {\n  return (source) => {\n    let latest, emitted = !1;\n    const { predicate, ...shareConfig } = config, wrapped = source.pipe(\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_17__.tap)((value) => {\n        config.predicate(value) && (emitted = !0, latest = value);\n      }),\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_18__.finalize)(() => {\n        emitted = !1, latest = void 0;\n      }),\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_19__.share)(shareConfig)\n    ), emitLatest = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable((subscriber) => {\n      emitted && subscriber.next(\n        // this cast is safe because of the emitted check which asserts that we got T from the source\n        latest\n      ), subscriber.complete();\n    });\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_20__.merge)(wrapped, emitLatest);\n  };\n}\nconst requiredApiVersion = \"2021-03-25\";\nclass LiveClient {\n  #client;\n  constructor(client) {\n    this.#client = client;\n  }\n  /**\n   * Requires `apiVersion` to be `2021-03-25` or later.\n   */\n  events({\n    includeDrafts = !1,\n    tag: _tag\n  } = {}) {\n    (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"live\", this.#client.config());\n    const {\n      projectId,\n      apiVersion: _apiVersion,\n      token,\n      withCredentials,\n      requestTagPrefix\n    } = this.#client.config(), apiVersion = _apiVersion.replace(/^v/, \"\");\n    if (apiVersion !== \"X\" && apiVersion < requiredApiVersion)\n      throw new Error(\n        `The live events API requires API version ${requiredApiVersion} or later. The current API version is ${apiVersion}. Please update your API version to use this feature.`\n      );\n    if (includeDrafts && !token && !withCredentials)\n      throw new Error(\n        \"The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.\"\n      );\n    const path = _getDataUrl(this.#client, \"live/events\"), url = new URL(this.#client.getUrl(path, !1)), tag = _tag && requestTagPrefix ? [requestTagPrefix, _tag].join(\".\") : _tag;\n    tag && url.searchParams.set(\"tag\", tag), includeDrafts && url.searchParams.set(\"includeDrafts\", \"true\");\n    const esOptions = {};\n    includeDrafts && token && (esOptions.headers = {\n      Authorization: `Bearer ${token}`\n    }), includeDrafts && withCredentials && (esOptions.withCredentials = !0);\n    const key = `${url.href}::${JSON.stringify(esOptions)}`, existing = eventsCache.get(key);\n    if (existing)\n      return existing;\n    const events = connectEventSource(() => (\n      // use polyfill if there is no global EventSource or if we need to set headers\n      (typeof EventSource > \"u\" || esOptions.headers ? eventSourcePolyfill : (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(EventSource)).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((EventSource2) => new EventSource2(url.href, esOptions)))\n    ), [\n      \"message\",\n      \"restart\",\n      \"welcome\",\n      \"reconnect\",\n      \"goaway\"\n    ]).pipe(\n      reconnectOnConnectionFailure(),\n      (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.map)((event) => {\n        if (event.type === \"message\") {\n          const { data, ...rest } = event;\n          return { ...rest, tags: data.tags };\n        }\n        return event;\n      })\n    ), checkCors = fetchObservable(url, {\n      method: \"OPTIONS\",\n      mode: \"cors\",\n      credentials: esOptions.withCredentials ? \"include\" : \"omit\",\n      headers: esOptions.headers\n    }).pipe(\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_6__.mergeMap)(() => rxjs__WEBPACK_IMPORTED_MODULE_21__.EMPTY),\n      (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.catchError)(() => {\n        throw new CorsOriginError({ projectId });\n      })\n    ), observable2 = (0,rxjs__WEBPACK_IMPORTED_MODULE_14__.concat)(checkCors, events).pipe(\n      (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.finalize)(() => eventsCache.delete(key)),\n      shareReplayLatest({\n        predicate: (event) => event.type === \"welcome\"\n      })\n    );\n    return eventsCache.set(key, observable2), observable2;\n  }\n}\nfunction fetchObservable(url, init) {\n  return new rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable((observer) => {\n    const controller = new AbortController(), signal = controller.signal;\n    return fetch(url, { ...init, signal: controller.signal }).then(\n      (response) => {\n        observer.next(response), observer.complete();\n      },\n      (err) => {\n        signal.aborted || observer.error(err);\n      }\n    ), () => controller.abort();\n  });\n}\nconst eventsCache = /* @__PURE__ */ new Map();\nclass ObservableDatasetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name2, options) {\n    return _modify(this.#client, this.#httpRequest, \"PUT\", name2, options);\n  }\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name2, options) {\n    return _modify(this.#client, this.#httpRequest, \"PATCH\", name2, options);\n  }\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name2) {\n    return _modify(this.#client, this.#httpRequest, \"DELETE\", name2);\n  }\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list() {\n    return _request(this.#client, this.#httpRequest, {\n      uri: \"/datasets\",\n      tag: null\n    });\n  }\n}\nclass DatasetsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Create a new dataset with the given name\n   *\n   * @param name - Name of the dataset to create\n   * @param options - Options for the dataset\n   */\n  create(name2, options) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"dataset\", this.#client.config()), (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _modify(this.#client, this.#httpRequest, \"PUT\", name2, options)\n    );\n  }\n  /**\n   * Edit a dataset with the given name\n   *\n   * @param name - Name of the dataset to edit\n   * @param options - New options for the dataset\n   */\n  edit(name2, options) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"dataset\", this.#client.config()), (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _modify(this.#client, this.#httpRequest, \"PATCH\", name2, options)\n    );\n  }\n  /**\n   * Delete a dataset with the given name\n   *\n   * @param name - Name of the dataset to delete\n   */\n  delete(name2) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"dataset\", this.#client.config()), (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_modify(this.#client, this.#httpRequest, \"DELETE\", name2));\n  }\n  /**\n   * Fetch a list of datasets for the configured project\n   */\n  list() {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"dataset\", this.#client.config()), (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _request(this.#client, this.#httpRequest, { uri: \"/datasets\", tag: null })\n    );\n  }\n}\nfunction _modify(client, httpRequest, method, name2, options) {\n  return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"dataset\", client.config()), (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.dataset)(name2), _request(client, httpRequest, {\n    method,\n    uri: `/datasets/${name2}`,\n    body: options,\n    tag: null\n  });\n}\nclass ObservableProjectsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  list(options) {\n    (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"projects\", this.#client.config());\n    const uri = options?.includeMembers === !1 ? \"/projects?includeMembers=false\" : \"/projects\";\n    return _request(this.#client, this.#httpRequest, { uri });\n  }\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"projects\", this.#client.config()), _request(this.#client, this.#httpRequest, { uri: `/projects/${projectId}` });\n  }\n}\nclass ProjectsClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  list(options) {\n    (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"projects\", this.#client.config());\n    const uri = options?.includeMembers === !1 ? \"/projects?includeMembers=false\" : \"/projects\";\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_request(this.#client, this.#httpRequest, { uri }));\n  }\n  /**\n   * Fetch a project by project ID\n   *\n   * @param projectId - ID of the project to fetch\n   */\n  getById(projectId) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.resourceGuard)(\"projects\", this.#client.config()), (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _request(this.#client, this.#httpRequest, { uri: `/projects/${projectId}` })\n    );\n  }\n}\nclass ObservableUsersClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById(id) {\n    return _request(\n      this.#client,\n      this.#httpRequest,\n      { uri: `/users/${id}` }\n    );\n  }\n}\nclass UsersClient {\n  #client;\n  #httpRequest;\n  constructor(client, httpRequest) {\n    this.#client = client, this.#httpRequest = httpRequest;\n  }\n  /**\n   * Fetch a user by user ID\n   *\n   * @param id - User ID of the user to fetch. If `me` is provided, a minimal response including the users role is returned.\n   */\n  getById(id) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _request(this.#client, this.#httpRequest, {\n        uri: `/users/${id}`\n      })\n    );\n  }\n}\nclass ObservableSanityClient {\n  assets;\n  datasets;\n  live;\n  projects;\n  users;\n  agent;\n  /**\n   * Private properties\n   */\n  #clientConfig;\n  #httpRequest;\n  /**\n   * Instance properties\n   */\n  listen = _listen;\n  constructor(httpRequest, config = _chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.defaultConfig) {\n    this.config(config), this.#httpRequest = httpRequest, this.assets = new ObservableAssetsClient(this, this.#httpRequest), this.datasets = new ObservableDatasetsClient(this, this.#httpRequest), this.live = new LiveClient(this), this.projects = new ObservableProjectsClient(this, this.#httpRequest), this.users = new ObservableUsersClient(this, this.#httpRequest), this.agent = {\n      action: new ObservableAgentsActionClient(this, this.#httpRequest)\n    };\n  }\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone() {\n    return new ObservableSanityClient(this.#httpRequest, this.config());\n  }\n  config(newConfig) {\n    if (newConfig === void 0)\n      return { ...this.#clientConfig };\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === !1)\n      throw new Error(\n        \"Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client\"\n      );\n    return this.#clientConfig = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.initConfig)(newConfig, this.#clientConfig || {}), this;\n  }\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig) {\n    const thisConfig = this.config();\n    return new ObservableSanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...thisConfig.stega || {},\n        ...typeof newConfig?.stega == \"boolean\" ? { enabled: newConfig.stega } : newConfig?.stega || {}\n      }\n    });\n  }\n  fetch(query, params, options) {\n    return _fetch(\n      this,\n      this.#httpRequest,\n      this.#clientConfig.stega,\n      query,\n      params,\n      options\n    );\n  }\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument(id, options) {\n    return _getDocument(this, this.#httpRequest, id, options);\n  }\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments(ids, options) {\n    return _getDocuments(this, this.#httpRequest, ids, options);\n  }\n  create(document, options) {\n    return _create(this, this.#httpRequest, document, \"create\", options);\n  }\n  createIfNotExists(document, options) {\n    return _createIfNotExists(this, this.#httpRequest, document, options);\n  }\n  createOrReplace(document, options) {\n    return _createOrReplace(this, this.#httpRequest, document, options);\n  }\n  delete(selection, options) {\n    return _delete(this, this.#httpRequest, selection, options);\n  }\n  mutate(operations, options) {\n    return _mutate(this, this.#httpRequest, operations, options);\n  }\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(selection, operations) {\n    return new ObservablePatch(selection, operations, this);\n  }\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction(operations) {\n    return new ObservableTransaction(operations, this);\n  }\n  /**\n   * Perform action operations against the configured dataset\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(operations, options) {\n    return _action(this, this.#httpRequest, operations, options);\n  }\n  /**\n   * Perform an HTTP request against the Sanity API\n   *\n   * @param options - Request options\n   */\n  request(options) {\n    return _request(this, this.#httpRequest, options);\n  }\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri, canUseCdn) {\n    return _getUrl(this, uri, canUseCdn);\n  }\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation, path) {\n    return _getDataUrl(this, operation, path);\n  }\n}\nclass SanityClient {\n  assets;\n  datasets;\n  live;\n  projects;\n  users;\n  agent;\n  /**\n   * Observable version of the Sanity client, with the same configuration as the promise-based one\n   */\n  observable;\n  /**\n   * Private properties\n   */\n  #clientConfig;\n  #httpRequest;\n  /**\n   * Instance properties\n   */\n  listen = _listen;\n  constructor(httpRequest, config = _chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.defaultConfig) {\n    this.config(config), this.#httpRequest = httpRequest, this.assets = new AssetsClient(this, this.#httpRequest), this.datasets = new DatasetsClient(this, this.#httpRequest), this.live = new LiveClient(this), this.projects = new ProjectsClient(this, this.#httpRequest), this.users = new UsersClient(this, this.#httpRequest), this.agent = {\n      action: new AgentActionsClient(this, this.#httpRequest)\n    }, this.observable = new ObservableSanityClient(httpRequest, config);\n  }\n  /**\n   * Clone the client - returns a new instance\n   */\n  clone() {\n    return new SanityClient(this.#httpRequest, this.config());\n  }\n  config(newConfig) {\n    if (newConfig === void 0)\n      return { ...this.#clientConfig };\n    if (this.#clientConfig && this.#clientConfig.allowReconfigure === !1)\n      throw new Error(\n        \"Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client\"\n      );\n    return this.observable && this.observable.config(newConfig), this.#clientConfig = (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.initConfig)(newConfig, this.#clientConfig || {}), this;\n  }\n  /**\n   * Clone the client with a new (partial) configuration.\n   *\n   * @param newConfig - New client configuration properties, shallowly merged with existing configuration\n   */\n  withConfig(newConfig) {\n    const thisConfig = this.config();\n    return new SanityClient(this.#httpRequest, {\n      ...thisConfig,\n      ...newConfig,\n      stega: {\n        ...thisConfig.stega || {},\n        ...typeof newConfig?.stega == \"boolean\" ? { enabled: newConfig.stega } : newConfig?.stega || {}\n      }\n    });\n  }\n  fetch(query, params, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _fetch(\n        this,\n        this.#httpRequest,\n        this.#clientConfig.stega,\n        query,\n        params,\n        options\n      )\n    );\n  }\n  /**\n   * Fetch a single document with the given ID.\n   *\n   * @param id - Document ID to fetch\n   * @param options - Request options\n   */\n  getDocument(id, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_getDocument(this, this.#httpRequest, id, options));\n  }\n  /**\n   * Fetch multiple documents in one request.\n   * Should be used sparingly - performing a query is usually a better option.\n   * The order/position of documents is preserved based on the original array of IDs.\n   * If any of the documents are missing, they will be replaced by a `null` entry in the returned array\n   *\n   * @param ids - Document IDs to fetch\n   * @param options - Request options\n   */\n  getDocuments(ids, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_getDocuments(this, this.#httpRequest, ids, options));\n  }\n  create(document, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _create(this, this.#httpRequest, document, \"create\", options)\n    );\n  }\n  createIfNotExists(document, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _createIfNotExists(this, this.#httpRequest, document, options)\n    );\n  }\n  createOrReplace(document, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(\n      _createOrReplace(this, this.#httpRequest, document, options)\n    );\n  }\n  delete(selection, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_delete(this, this.#httpRequest, selection, options));\n  }\n  mutate(operations, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_mutate(this, this.#httpRequest, operations, options));\n  }\n  /**\n   * Create a new buildable patch of operations to perform\n   *\n   * @param selection - Document ID, an array of document IDs, or an object with `query` and optional `params`, defining which document(s) to patch\n   * @param operations - Optional object of patch operations to initialize the patch instance with\n   * @returns Patch instance - call `.commit()` to perform the operations defined\n   */\n  patch(documentId, operations) {\n    return new Patch(documentId, operations, this);\n  }\n  /**\n   * Create a new transaction of mutations\n   *\n   * @param operations - Optional array of mutation operations to initialize the transaction instance with\n   */\n  transaction(operations) {\n    return new Transaction(operations, this);\n  }\n  /**\n   * Perform action operations against the configured dataset\n   * Returns a promise that resolves to the transaction result\n   *\n   * @param operations - Action operation(s) to execute\n   * @param options - Action options\n   */\n  action(operations, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_action(this, this.#httpRequest, operations, options));\n  }\n  /**\n   * Perform a request against the Sanity API\n   * NOTE: Only use this for Sanity API endpoints, not for your own APIs!\n   *\n   * @param options - Request options\n   * @returns Promise resolving to the response body\n   */\n  request(options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_request(this, this.#httpRequest, options));\n  }\n  /**\n   * Perform an HTTP request a `/data` sub-endpoint\n   * NOTE: Considered internal, thus marked as deprecated. Use `request` instead.\n   *\n   * @deprecated - Use `request()` or your own HTTP library instead\n   * @param endpoint - Endpoint to hit (mutate, query etc)\n   * @param body - Request body\n   * @param options - Request options\n   * @internal\n   */\n  dataRequest(endpoint, body, options) {\n    return (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.lastValueFrom)(_dataRequest(this, this.#httpRequest, endpoint, body, options));\n  }\n  /**\n   * Get a Sanity API URL for the URI provided\n   *\n   * @param uri - URI/path to build URL for\n   * @param canUseCdn - Whether or not to allow using the API CDN for this route\n   */\n  getUrl(uri, canUseCdn) {\n    return _getUrl(this, uri, canUseCdn);\n  }\n  /**\n   * Get a Sanity API URL for the data operation and path provided\n   *\n   * @param operation - Data operation (eg `query`, `mutate`, `listen` or similar)\n   * @param path - Path to append after the operation\n   */\n  getDataUrl(operation, path) {\n    return _getDataUrl(this, operation, path);\n  }\n}\nfunction defineCreateClientExports(envMiddleware, ClassConstructor) {\n  return { requester: defineHttpRequest(envMiddleware), createClient: (config) => {\n    const clientRequester = defineHttpRequest(envMiddleware);\n    return new ClassConstructor(\n      (options, requester2) => (requester2 || clientRequester)({\n        maxRedirects: 0,\n        maxRetries: config.maxRetries,\n        retryDelay: config.retryDelay,\n        ...options\n      }),\n      config\n    );\n  } };\n}\nfunction defineDeprecatedCreateClient(createClient2) {\n  return function(config) {\n    return (0,_chunks_es_config_js__WEBPACK_IMPORTED_MODULE_7__.printNoDefaultExport)(), createClient2(config);\n  };\n}\nvar name = \"@sanity/client\", version = \"7.1.0\";\nconst middleware = [\n  (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.debug)({ verbose: !0, namespace: \"sanity:client\" }),\n  (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.headers)({ \"User-Agent\": `${name} ${version}` }),\n  // Enable keep-alive, and in addition limit the number of sockets that can be opened.\n  // This avoids opening too many connections to the server if someone tries to execute\n  // a bunch of requests in parallel. It's recommended to have a concurrency limit\n  // at a \"higher limit\" (i.e. you shouldn't actually execute hundreds of requests in parallel),\n  // and this is mainly to minimize the impact for the network and server.\n  //\n  // We're currently matching the same defaults as browsers:\n  // https://stackoverflow.com/questions/26003756/is-there-a-limit-practical-or-otherwise-to-the-number-of-web-sockets-a-page-op\n  (0,get_it_middleware__WEBPACK_IMPORTED_MODULE_1__.agent)({\n    keepAlive: !0,\n    maxSockets: 30,\n    maxTotalSockets: 256\n  })\n], exp = defineCreateClientExports(middleware, SanityClient), requester = exp.requester, createClient = exp.createClient, deprecatedCreateClient = defineDeprecatedCreateClient(createClient);\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9jbGllbnQvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ2U7QUFDb0U7QUFDZ0U7QUFDMUg7QUFDZ0M7QUFDdVA7QUFDL1U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixZQUFZLElBQUksYUFBYTtBQUMzRDtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0osS0FBSztBQUNMO0FBQ0EsU0FBUyxxQkFBcUIsMkJBQTJCLHVCQUF1QixFQUFFLFNBQVM7QUFDM0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Qsa0JBQWtCO0FBQ2xFLFlBQVksV0FBVyxjQUFjLFNBQVMsbUJBQW1CLGVBQWUsRUFBRSxjQUFjO0FBQ2hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFdBQVc7QUFDM0I7QUFDQSw0REFBNEQsVUFBVTtBQUN0RTtBQUNBLGNBQWMsU0FBUztBQUN2QixpTkFBaU4sSUFBSTtBQUNyTixNQUFNO0FBQ04sOEhBQThILElBQUk7QUFDbEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsNkNBQUs7QUFDZCxJQUFJLHdEQUFLLEdBQUcsYUFBYTtBQUN6QjtBQUNBO0FBQ0EsSUFBSSw4REFBVztBQUNmLElBQUksK0RBQVk7QUFDaEIsSUFBSSwyREFBUTtBQUNaO0FBQ0EsSUFBSSw2REFBVSxHQUFHLGdCQUFnQiw0Q0FBVSxFQUFFO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsb0RBQUs7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsMkNBQUs7QUFDZDtBQUNBLFdBQVcsa0RBQVksWUFBWSx3Q0FBRTtBQUNyQyxHQUFHLE9BQU8sOENBQVE7QUFDbEI7QUFDQTtBQUNBLGFBQWEsNENBQVU7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRGQUE0RixjQUFjO0FBQzFHO0FBQ0E7QUFDQTtBQUNBLGtKQUFrSixtQkFBbUI7QUFDcks7QUFDQTtBQUNBLHNCQUFzQixjQUFjO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5RUFBeUUsbUJBQW1CO0FBQzVGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLHNDQUFzQztBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixtQkFBbUI7QUFDN0MsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsSUFBSTtBQUN4QztBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsYUFBYSxtQ0FBbUM7QUFDaEQ7QUFDQSx1RkFBdUYsdUNBQXVDLElBQUk7QUFDbEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsRUFBRSxjQUFjO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFGQUFxRjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUZBQXFGO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzR0FBc0c7QUFDdEc7QUFDQTtBQUNBLFdBQVcsb0VBQWM7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMscUJBQXFCLGNBQWM7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9FQUFjLGdEQUFnRCx1QkFBdUI7QUFDaEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxTQUFTO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsU0FBUztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlQQUF5UCxTQUFTLEdBQUcsV0FBVyxHQUFHLFNBQVM7QUFDNVI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQSxXQUFXLG9FQUFjLCtDQUErQztBQUN4RSw0QkFBNEIscUNBQXFDO0FBQ2pFLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELG9CQUFvQjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrRkFBa0Ysa0NBQWtDO0FBQ3BILGlDQUFpQyx5QkFBeUI7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxvQkFBb0I7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0ZBQWtGLGtDQUFrQztBQUNwSCxpQ0FBaUMseUJBQXlCO0FBQzFEO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9FQUFjLDZCQUE2QixhQUFhO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0VBQWMsV0FBVyx1RUFBaUIsdUJBQXVCLFdBQVc7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvRUFBYyxXQUFXLHVFQUFpQix1QkFBdUIsV0FBVztBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx3RUFBa0Isb0NBQW9DLFVBQVUsa0JBQWtCO0FBQzdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMkJBQTJCLHFDQUFxQztBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHNDQUFzQztBQUMvRDtBQUNBLDREQUE0RDtBQUM1RDtBQUNBO0FBQ0EseUJBQXlCLDBCQUEwQjtBQUNuRDtBQUNBO0FBQ0EsK0RBQStEO0FBQy9ELHlCQUF5QiwwQkFBMEI7QUFDbkQ7QUFDQSx1QkFBdUIsU0FBUyxzQ0FBc0M7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwyQkFBMkIscUNBQXFDO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsc0NBQXNDO0FBQy9EO0FBQ0Esc0VBQXNFO0FBQ3RFO0FBQ0E7QUFDQSx5QkFBeUIsMEJBQTBCO0FBQ25EO0FBQ0EsdUJBQXVCLFNBQVMsc0NBQXNDO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QztBQUM5QyxxQkFBcUI7QUFDckIsK0NBQStDLE1BQU07QUFDckQ7QUFDQSx5QkFBeUI7QUFDekIsNkJBQTZCLG1DQUFtQztBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFGQUFxRixzQ0FBc0M7QUFDM0gsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLENBQUM7QUFDRCxnREFBZ0QsOENBQThDO0FBQzlGO0FBQ0E7QUFDQSw0QkFBNEIsSUFBSTtBQUNoQztBQUNBLHlDQUF5QyxNQUFNO0FBQy9DLDZKQUE2SixhQUFhO0FBQzFLLENBQUMsc0lBQXNJO0FBQ3ZJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxnRUFBZ0UsY0FBYztBQUM5RTtBQUNBLG1CQUFtQjtBQUNuQiw2Q0FBNkMseUJBQXlCO0FBQ3RFLElBQUksbUNBQW1DLG9FQUFVLHlHQUF5Ryx1QkFBdUI7QUFDakw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyx3REFBd0Qsa0JBQWtCLGdCQUFnQixnRUFBZ0UsZUFBZTtBQUM1SztBQUNBLElBQUksaUVBQWlCO0FBQ3JCLE1BQU0sMkNBQUk7QUFDVixRQUFRLDZPQUE4QztBQUN0RDtBQUNBLFNBQVM7QUFDVCxhQUFhLHNCQUFzQjtBQUNuQztBQUNBO0FBQ0E7QUFDQSxJQUFJLG1EQUFHO0FBQ1A7QUFDQTtBQUNBLDZCQUE2QixnQkFBZ0I7QUFDN0M7QUFDQTtBQUNBLG9CQUFvQixtREFBRztBQUN2QjtBQUNBLHdEQUF3RDtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksc0RBQU07QUFDVixJQUFJLG1EQUFHO0FBQ1A7QUFDQTtBQUNBLDBEQUEwRDtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksc0RBQU07QUFDVixJQUFJLG1EQUFHO0FBQ1A7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxTQUFTLHVFQUFpQjtBQUMxQjtBQUNBO0FBQ0EsU0FBUyx1RUFBaUI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxjQUFjLGlDQUFpQyxHQUFHO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsK0JBQStCO0FBQzlHO0FBQ0EsdURBQXVELGdDQUFnQztBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sMkVBQTJFO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBLHVFQUF1RTtBQUN2RSx1VUFBdVUsa0ZBQWtGO0FBQ3paO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksc0RBQU07QUFDVixJQUFJLG1EQUFHO0FBQ1AsSUFBSSxtREFBRztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLDJEQUEyRDtBQUMzRCxxQkFBcUIsV0FBVyx5QkFBeUIsc0NBQXNDO0FBQy9GLHVEQUF1RCx1QkFBdUI7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELEtBQUssZ0VBQVUseUJBQXlCO0FBQ2hHO0FBQ0EsK0VBQStFLG1DQUFtQztBQUNsSDtBQUNBLGdGQUFnRiwwRkFBb0MsSUFBSSw0RUFBc0I7QUFDOUk7QUFDQTtBQUNBLEtBQUs7QUFDTCx3R0FBd0csa0ZBQTRCLG9EQUFvRCw0REFBNEQsb0RBQW9ELHdDQUF3QyxrRUFBa0Usd0NBQXdDO0FBQzFiO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBLEtBQUs7QUFDTCxtQkFBbUIsNENBQVU7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxzREFBTTtBQUNWLElBQUksbURBQUc7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxvRUFBYztBQUNsQiwrRUFBK0UsVUFBVSxHQUFHLEtBQUs7QUFDakcsY0FBYyxhQUFhLEdBQUcsS0FBSztBQUNuQztBQUNBLGtCQUFrQixnRUFBVSx3QkFBd0IsVUFBVSxHQUFHLFFBQVE7QUFDekUsaUJBQWlCLHFCQUFxQixRQUFRLEdBQUcsS0FBSyxZQUFZO0FBQ2xFO0FBQ0E7QUFDQSxVQUFVLGNBQWM7QUFDeEIsWUFBWSx5QkFBeUIsR0FBRyx1QkFBdUI7QUFDL0Q7QUFDQTtBQUNBLHdCQUF3Qiw0Q0FBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsV0FBVztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLFlBQVksWUFBWSxZQUFZO0FBQzlEO0FBQ0E7QUFDQSwwQkFBMEIsR0FBRztBQUM3QjtBQUNBLGlDQUFpQyxHQUFHO0FBQ3BDO0FBQ0EsNEJBQTRCLEdBQUc7QUFDL0I7QUFDQSxvREFBb0QsZ0JBQWdCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnRUFBVTtBQUM3QjtBQUNBO0FBQ0EsbUNBQW1DLFNBQVM7QUFDNUM7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLG1CQUFtQixnRUFBVTtBQUM3QjtBQUNBO0FBQ0Esb0NBQW9DLFNBQVM7QUFDN0M7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLG1CQUFtQixnRUFBVTtBQUM3QjtBQUNBO0FBQ0Esb0NBQW9DLFNBQVM7QUFDN0M7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9EQUFhO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvREFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvREFBYTtBQUN4QjtBQUNBLFFBQVEsc0RBQU07QUFDZCxRQUFRLG1EQUFHO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFO0FBQ2hFLEVBQUUsdUVBQWlCO0FBQ25CO0FBQ0E7QUFDQSwyRUFBMkUsK0RBQStEO0FBQzFJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxzQ0FBc0MsSUFBSTtBQUMvRTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBLDRGQUE0Riw2Q0FBNkM7QUFDekk7QUFDQTtBQUNBLDRCQUE0QixHQUFHLFVBQVUsa0JBQWtCO0FBQzNEO0FBQ0EsbUNBQW1DLEdBQUc7QUFDdEM7QUFDQSw4QkFBOEIsR0FBRyxVQUFVLGtCQUFrQjtBQUM3RDtBQUNBLHNEQUFzRCxnQkFBZ0I7QUFDdEU7QUFDQTtBQUNBLG1CQUFtQixnRUFBVTtBQUM3QixtQkFBbUIsa0JBQWtCLEdBQUcsU0FBUztBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsNkxBQTZMO0FBQzdMLHVJQUF1SSx5QkFBeUIsMkNBQUssT0FBTyx1TUFBNkI7QUFDek0sRUFBRSxtREFBRyxJQUFJLHVCQUF1QjtBQUNoQyxFQUFFLGtEQUFXO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLGlEQUFVLHlEQUF5RCw2Q0FBTSxDQUFDLHdDQUFFLEdBQUcsbUJBQW1CLEdBQUcsNENBQUssV0FBVyw4Q0FBUSxtQkFBbUIsaURBQVU7QUFDaEs7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QztBQUN6QyxVQUFVLGdEQUFnRCxxSEFBcUgsd0NBQXdDLHdFQUF3RSwwQkFBMEIsc0JBQXNCLFlBQVksSUFBSSxFQUFFLGdDQUFnQztBQUNqWTtBQUNBLFdBQVcsaURBQVU7QUFDckI7QUFDQTtBQUNBLDZCQUE2QixNQUFNO0FBQ25DLEdBQUc7QUFDSDtBQUNBLDJFQUEyRSx3Q0FBRSxvQkFBb0IsbURBQUc7QUFDcEc7QUFDQTtBQUNBLElBQUksc0RBQU07QUFDVixJQUFJLG1EQUFHO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsMENBQTBDO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLDRCQUE0QjtBQUN4QyxNQUFNLDBDQUFHO0FBQ1Q7QUFDQSxPQUFPO0FBQ1AsTUFBTSwrQ0FBUTtBQUNkO0FBQ0EsT0FBTztBQUNQLE1BQU0sNENBQUs7QUFDWCx3QkFBd0IsNENBQVU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsV0FBVyw0Q0FBSztBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxJQUFJO0FBQ1IsSUFBSSxtRUFBYTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLG9EQUFvRCxvQkFBb0IsdUNBQXVDLFdBQVc7QUFDMUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLE1BQU07QUFDckMsS0FBSztBQUNMLG1CQUFtQixTQUFTLElBQUksMEJBQTBCO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkVBQTZFLHdDQUFFLG9CQUFvQixtREFBRztBQUN0RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxtREFBRztBQUNUO0FBQ0Esa0JBQWtCLGdCQUFnQjtBQUNsQyxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLE1BQU0sOENBQVEsT0FBTyx3Q0FBSztBQUMxQixNQUFNLGlEQUFVO0FBQ2hCLG9DQUFvQyxXQUFXO0FBQy9DLE9BQU87QUFDUCxxQkFBcUIsNkNBQU07QUFDM0IsTUFBTSx3REFBVTtBQUNoQjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDRDQUFVO0FBQ3ZCO0FBQ0Esd0JBQXdCLG9DQUFvQztBQUM1RDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtRUFBYSxvQ0FBb0Msb0RBQWE7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1FQUFhLG9DQUFvQyxvREFBYTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG1FQUFhLG9DQUFvQyxvREFBYTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtRUFBYSxvQ0FBb0Msb0RBQWE7QUFDekUsa0RBQWtELDZCQUE2QjtBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsbUVBQWEsOEJBQThCLDZEQUFPO0FBQzNEO0FBQ0Esc0JBQXNCLE1BQU07QUFDNUI7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxtRUFBYTtBQUNqQjtBQUNBLHVEQUF1RCxLQUFLO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtRUFBYSxpRkFBaUYsa0JBQWtCLFVBQVUsR0FBRztBQUN4STtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLG1FQUFhO0FBQ2pCO0FBQ0EsV0FBVyxvREFBYSw2Q0FBNkMsS0FBSztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsbUVBQWEscUNBQXFDLG9EQUFhO0FBQzFFLGtEQUFrRCxrQkFBa0IsVUFBVSxHQUFHO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsZUFBZSxHQUFHO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQSx1QkFBdUIsR0FBRztBQUMxQixPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsK0RBQWE7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGdFQUFVLG9DQUFvQztBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsb0RBQW9ELDJCQUEyQjtBQUMvRTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQywrREFBYTtBQUNqRDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNGQUFzRixnRUFBVSxvQ0FBb0M7QUFDcEk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLG9EQUFvRCwyQkFBMkI7QUFDL0U7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9EQUFhO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvREFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxvREFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9EQUFhO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsb0RBQWE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMEVBQW9CO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSx3REFBSyxHQUFHLHlDQUF5QztBQUNuRCxFQUFFLDBEQUFPLEdBQUcsaUJBQWlCLE1BQU0sRUFBRSxRQUFRLEdBQUc7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsd0RBQUs7QUFDUDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUF5QkU7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFJlbnRlck5nXFxyZW50ZXJzbmctYXBwXFxub2RlX21vZHVsZXNcXEBzYW5pdHlcXGNsaWVudFxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0SXQgfSBmcm9tIFwiZ2V0LWl0XCI7XG5pbXBvcnQgeyBhZGFwdGVyLCBlbnZpcm9ubWVudCB9IGZyb20gXCJnZXQtaXRcIjtcbmltcG9ydCB7IHJldHJ5LCBqc29uUmVxdWVzdCwganNvblJlc3BvbnNlLCBwcm9ncmVzcywgb2JzZXJ2YWJsZSwgZGVidWcsIGhlYWRlcnMsIGFnZW50IH0gZnJvbSBcImdldC1pdC9taWRkbGV3YXJlXCI7XG5pbXBvcnQgeyBPYnNlcnZhYmxlLCBkZWZlciwgb2YsIGlzT2JzZXJ2YWJsZSwgbWVyZ2VNYXAsIGZyb20sIGxhc3RWYWx1ZUZyb20sIHNoYXJlUmVwbGF5LCBjYXRjaEVycm9yLCBjb25jYXQsIHRocm93RXJyb3IsIHRpbWVyLCB0YXAsIGZpbmFsaXplLCBzaGFyZSwgbWVyZ2UsIEVNUFRZIH0gZnJvbSBcInJ4anNcIjtcbmltcG9ydCB7IHN0ZWdhQ2xlYW4gfSBmcm9tIFwiLi9fY2h1bmtzLWVzL3N0ZWdhQ2xlYW4uanNcIjtcbmltcG9ydCB7IGNvbWJpbmVMYXRlc3RXaXRoLCBtYXAsIGZpbHRlciwgZmluYWxpemUgYXMgZmluYWxpemUkMSB9IGZyb20gXCJyeGpzL29wZXJhdG9yc1wiO1xuaW1wb3J0IHsgdmFsaWRhdGVPYmplY3QsIHZhbGlkYXRlSW5zZXJ0LCByZXF1aXJlRG9jdW1lbnRJZCwgdmFsaWRhdGVEb2N1bWVudElkLCByZXNvdXJjZUNvbmZpZywgaGFzRGF0YXNldCwgcmVxdWVzdFRhZywgcHJpbnRQcmV2aWV3RHJhZnRzRGVwcmVjYXRpb25XYXJuaW5nLCB2YWxpZGF0ZUFwaVBlcnNwZWN0aXZlLCBwcmludENkblByZXZpZXdEcmFmdHNXYXJuaW5nLCB2YWxpZGF0ZUFzc2V0VHlwZSwgcmVzb3VyY2VHdWFyZCwgZGF0YXNldCwgZGVmYXVsdENvbmZpZywgaW5pdENvbmZpZywgcHJpbnROb0RlZmF1bHRFeHBvcnQgfSBmcm9tIFwiLi9fY2h1bmtzLWVzL2NvbmZpZy5qc1wiO1xuY2xhc3MgQ2xpZW50RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIHJlc3BvbnNlO1xuICBzdGF0dXNDb2RlID0gNDAwO1xuICByZXNwb25zZUJvZHk7XG4gIGRldGFpbHM7XG4gIGNvbnN0cnVjdG9yKHJlcykge1xuICAgIGNvbnN0IHByb3BzID0gZXh0cmFjdEVycm9yUHJvcHMocmVzKTtcbiAgICBzdXBlcihwcm9wcy5tZXNzYWdlKSwgT2JqZWN0LmFzc2lnbih0aGlzLCBwcm9wcyk7XG4gIH1cbn1cbmNsYXNzIFNlcnZlckVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICByZXNwb25zZTtcbiAgc3RhdHVzQ29kZSA9IDUwMDtcbiAgcmVzcG9uc2VCb2R5O1xuICBkZXRhaWxzO1xuICBjb25zdHJ1Y3RvcihyZXMpIHtcbiAgICBjb25zdCBwcm9wcyA9IGV4dHJhY3RFcnJvclByb3BzKHJlcyk7XG4gICAgc3VwZXIocHJvcHMubWVzc2FnZSksIE9iamVjdC5hc3NpZ24odGhpcywgcHJvcHMpO1xuICB9XG59XG5mdW5jdGlvbiBleHRyYWN0RXJyb3JQcm9wcyhyZXMpIHtcbiAgY29uc3QgYm9keSA9IHJlcy5ib2R5LCBwcm9wcyA9IHtcbiAgICByZXNwb25zZTogcmVzLFxuICAgIHN0YXR1c0NvZGU6IHJlcy5zdGF0dXNDb2RlLFxuICAgIHJlc3BvbnNlQm9keTogc3RyaW5naWZ5Qm9keShib2R5LCByZXMpLFxuICAgIG1lc3NhZ2U6IFwiXCIsXG4gICAgZGV0YWlsczogdm9pZCAwXG4gIH07XG4gIGlmIChib2R5LmVycm9yICYmIGJvZHkubWVzc2FnZSlcbiAgICByZXR1cm4gcHJvcHMubWVzc2FnZSA9IGAke2JvZHkuZXJyb3J9IC0gJHtib2R5Lm1lc3NhZ2V9YCwgcHJvcHM7XG4gIGlmIChpc011dGF0aW9uRXJyb3IoYm9keSkgfHwgaXNBY3Rpb25FcnJvcihib2R5KSkge1xuICAgIGNvbnN0IGFsbEl0ZW1zID0gYm9keS5lcnJvci5pdGVtcyB8fCBbXSwgaXRlbXMgPSBhbGxJdGVtcy5zbGljZSgwLCA1KS5tYXAoKGl0ZW0pID0+IGl0ZW0uZXJyb3I/LmRlc2NyaXB0aW9uKS5maWx0ZXIoQm9vbGVhbik7XG4gICAgbGV0IGl0ZW1zU3RyID0gaXRlbXMubGVuZ3RoID8gYDpcbi0gJHtpdGVtcy5qb2luKGBcbi0gYCl9YCA6IFwiXCI7XG4gICAgcmV0dXJuIGFsbEl0ZW1zLmxlbmd0aCA+IDUgJiYgKGl0ZW1zU3RyICs9IGBcbi4uLmFuZCAke2FsbEl0ZW1zLmxlbmd0aCAtIDV9IG1vcmVgKSwgcHJvcHMubWVzc2FnZSA9IGAke2JvZHkuZXJyb3IuZGVzY3JpcHRpb259JHtpdGVtc1N0cn1gLCBwcm9wcy5kZXRhaWxzID0gYm9keS5lcnJvciwgcHJvcHM7XG4gIH1cbiAgcmV0dXJuIGJvZHkuZXJyb3IgJiYgYm9keS5lcnJvci5kZXNjcmlwdGlvbiA/IChwcm9wcy5tZXNzYWdlID0gYm9keS5lcnJvci5kZXNjcmlwdGlvbiwgcHJvcHMuZGV0YWlscyA9IGJvZHkuZXJyb3IsIHByb3BzKSA6IChwcm9wcy5tZXNzYWdlID0gYm9keS5lcnJvciB8fCBib2R5Lm1lc3NhZ2UgfHwgaHR0cEVycm9yTWVzc2FnZShyZXMpLCBwcm9wcyk7XG59XG5mdW5jdGlvbiBpc011dGF0aW9uRXJyb3IoYm9keSkge1xuICByZXR1cm4gaXNQbGFpbk9iamVjdChib2R5KSAmJiBpc1BsYWluT2JqZWN0KGJvZHkuZXJyb3IpICYmIGJvZHkuZXJyb3IudHlwZSA9PT0gXCJtdXRhdGlvbkVycm9yXCIgJiYgdHlwZW9mIGJvZHkuZXJyb3IuZGVzY3JpcHRpb24gPT0gXCJzdHJpbmdcIjtcbn1cbmZ1bmN0aW9uIGlzQWN0aW9uRXJyb3IoYm9keSkge1xuICByZXR1cm4gaXNQbGFpbk9iamVjdChib2R5KSAmJiBpc1BsYWluT2JqZWN0KGJvZHkuZXJyb3IpICYmIGJvZHkuZXJyb3IudHlwZSA9PT0gXCJhY3Rpb25FcnJvclwiICYmIHR5cGVvZiBib2R5LmVycm9yLmRlc2NyaXB0aW9uID09IFwic3RyaW5nXCI7XG59XG5mdW5jdGlvbiBpc1BsYWluT2JqZWN0KG9iaikge1xuICByZXR1cm4gdHlwZW9mIG9iaiA9PSBcIm9iamVjdFwiICYmIG9iaiAhPT0gbnVsbCAmJiAhQXJyYXkuaXNBcnJheShvYmopO1xufVxuZnVuY3Rpb24gaHR0cEVycm9yTWVzc2FnZShyZXMpIHtcbiAgY29uc3Qgc3RhdHVzTWVzc2FnZSA9IHJlcy5zdGF0dXNNZXNzYWdlID8gYCAke3Jlcy5zdGF0dXNNZXNzYWdlfWAgOiBcIlwiO1xuICByZXR1cm4gYCR7cmVzLm1ldGhvZH0tcmVxdWVzdCB0byAke3Jlcy51cmx9IHJlc3VsdGVkIGluIEhUVFAgJHtyZXMuc3RhdHVzQ29kZX0ke3N0YXR1c01lc3NhZ2V9YDtcbn1cbmZ1bmN0aW9uIHN0cmluZ2lmeUJvZHkoYm9keSwgcmVzKSB7XG4gIHJldHVybiAocmVzLmhlYWRlcnNbXCJjb250ZW50LXR5cGVcIl0gfHwgXCJcIikudG9Mb3dlckNhc2UoKS5pbmRleE9mKFwiYXBwbGljYXRpb24vanNvblwiKSAhPT0gLTEgPyBKU09OLnN0cmluZ2lmeShib2R5LCBudWxsLCAyKSA6IGJvZHk7XG59XG5jbGFzcyBDb3JzT3JpZ2luRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIHByb2plY3RJZDtcbiAgYWRkT3JpZ2luVXJsO1xuICBjb25zdHJ1Y3Rvcih7IHByb2plY3RJZCB9KSB7XG4gICAgc3VwZXIoXCJDb3JzT3JpZ2luRXJyb3JcIiksIHRoaXMubmFtZSA9IFwiQ29yc09yaWdpbkVycm9yXCIsIHRoaXMucHJvamVjdElkID0gcHJvamVjdElkO1xuICAgIGNvbnN0IHVybCA9IG5ldyBVUkwoYGh0dHBzOi8vc2FuaXR5LmlvL21hbmFnZS9wcm9qZWN0LyR7cHJvamVjdElkfS9hcGlgKTtcbiAgICBpZiAodHlwZW9mIGxvY2F0aW9uIDwgXCJ1XCIpIHtcbiAgICAgIGNvbnN0IHsgb3JpZ2luIH0gPSBsb2NhdGlvbjtcbiAgICAgIHVybC5zZWFyY2hQYXJhbXMuc2V0KFwiY29yc1wiLCBcImFkZFwiKSwgdXJsLnNlYXJjaFBhcmFtcy5zZXQoXCJvcmlnaW5cIiwgb3JpZ2luKSwgdGhpcy5hZGRPcmlnaW5VcmwgPSB1cmwsIHRoaXMubWVzc2FnZSA9IGBUaGUgY3VycmVudCBvcmlnaW4gaXMgbm90IGFsbG93ZWQgdG8gY29ubmVjdCB0byB0aGUgTGl2ZSBDb250ZW50IEFQSS4gQWRkIGl0IGhlcmU6ICR7dXJsfWA7XG4gICAgfSBlbHNlXG4gICAgICB0aGlzLm1lc3NhZ2UgPSBgVGhlIGN1cnJlbnQgb3JpZ2luIGlzIG5vdCBhbGxvd2VkIHRvIGNvbm5lY3QgdG8gdGhlIExpdmUgQ29udGVudCBBUEkuIENoYW5nZSB5b3VyIGNvbmZpZ3VyYXRpb24gaGVyZTogJHt1cmx9YDtcbiAgfVxufVxuY29uc3QgaHR0cEVycm9yID0ge1xuICBvblJlc3BvbnNlOiAocmVzKSA9PiB7XG4gICAgaWYgKHJlcy5zdGF0dXNDb2RlID49IDUwMClcbiAgICAgIHRocm93IG5ldyBTZXJ2ZXJFcnJvcihyZXMpO1xuICAgIGlmIChyZXMuc3RhdHVzQ29kZSA+PSA0MDApXG4gICAgICB0aHJvdyBuZXcgQ2xpZW50RXJyb3IocmVzKTtcbiAgICByZXR1cm4gcmVzO1xuICB9XG59O1xuZnVuY3Rpb24gcHJpbnRXYXJuaW5ncygpIHtcbiAgY29uc3Qgc2VlbiA9IHt9O1xuICByZXR1cm4ge1xuICAgIG9uUmVzcG9uc2U6IChyZXMpID0+IHtcbiAgICAgIGNvbnN0IHdhcm4gPSByZXMuaGVhZGVyc1tcIngtc2FuaXR5LXdhcm5pbmdcIl0sIHdhcm5pbmdzID0gQXJyYXkuaXNBcnJheSh3YXJuKSA/IHdhcm4gOiBbd2Fybl07XG4gICAgICBmb3IgKGNvbnN0IG1zZyBvZiB3YXJuaW5ncylcbiAgICAgICAgIW1zZyB8fCBzZWVuW21zZ10gfHwgKHNlZW5bbXNnXSA9ICEwLCBjb25zb2xlLndhcm4obXNnKSk7XG4gICAgICByZXR1cm4gcmVzO1xuICAgIH1cbiAgfTtcbn1cbmZ1bmN0aW9uIGRlZmluZUh0dHBSZXF1ZXN0KGVudk1pZGRsZXdhcmUpIHtcbiAgcmV0dXJuIGdldEl0KFtcbiAgICByZXRyeSh7IHNob3VsZFJldHJ5IH0pLFxuICAgIC4uLmVudk1pZGRsZXdhcmUsXG4gICAgcHJpbnRXYXJuaW5ncygpLFxuICAgIGpzb25SZXF1ZXN0KCksXG4gICAganNvblJlc3BvbnNlKCksXG4gICAgcHJvZ3Jlc3MoKSxcbiAgICBodHRwRXJyb3IsXG4gICAgb2JzZXJ2YWJsZSh7IGltcGxlbWVudGF0aW9uOiBPYnNlcnZhYmxlIH0pXG4gIF0pO1xufVxuZnVuY3Rpb24gc2hvdWxkUmV0cnkoZXJyLCBhdHRlbXB0LCBvcHRpb25zKSB7XG4gIGlmIChvcHRpb25zLm1heFJldHJpZXMgPT09IDApIHJldHVybiAhMTtcbiAgY29uc3QgaXNTYWZlID0gb3B0aW9ucy5tZXRob2QgPT09IFwiR0VUXCIgfHwgb3B0aW9ucy5tZXRob2QgPT09IFwiSEVBRFwiLCBpc1F1ZXJ5MiA9IChvcHRpb25zLnVyaSB8fCBvcHRpb25zLnVybCkuc3RhcnRzV2l0aChcIi9kYXRhL3F1ZXJ5XCIpLCBpc1JldHJpYWJsZVJlc3BvbnNlID0gZXJyLnJlc3BvbnNlICYmIChlcnIucmVzcG9uc2Uuc3RhdHVzQ29kZSA9PT0gNDI5IHx8IGVyci5yZXNwb25zZS5zdGF0dXNDb2RlID09PSA1MDIgfHwgZXJyLnJlc3BvbnNlLnN0YXR1c0NvZGUgPT09IDUwMyk7XG4gIHJldHVybiAoaXNTYWZlIHx8IGlzUXVlcnkyKSAmJiBpc1JldHJpYWJsZVJlc3BvbnNlID8gITAgOiByZXRyeS5zaG91bGRSZXRyeShlcnIsIGF0dGVtcHQsIG9wdGlvbnMpO1xufVxuY2xhc3MgQ29ubmVjdGlvbkZhaWxlZEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBuYW1lID0gXCJDb25uZWN0aW9uRmFpbGVkRXJyb3JcIjtcbn1cbmNsYXNzIERpc2Nvbm5lY3RFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgbmFtZSA9IFwiRGlzY29ubmVjdEVycm9yXCI7XG4gIHJlYXNvbjtcbiAgY29uc3RydWN0b3IobWVzc2FnZSwgcmVhc29uLCBvcHRpb25zID0ge30pIHtcbiAgICBzdXBlcihtZXNzYWdlLCBvcHRpb25zKSwgdGhpcy5yZWFzb24gPSByZWFzb247XG4gIH1cbn1cbmNsYXNzIENoYW5uZWxFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgbmFtZSA9IFwiQ2hhbm5lbEVycm9yXCI7XG4gIGRhdGE7XG4gIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIGRhdGEpIHtcbiAgICBzdXBlcihtZXNzYWdlKSwgdGhpcy5kYXRhID0gZGF0YTtcbiAgfVxufVxuY2xhc3MgTWVzc2FnZUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBuYW1lID0gXCJNZXNzYWdlRXJyb3JcIjtcbiAgZGF0YTtcbiAgY29uc3RydWN0b3IobWVzc2FnZSwgZGF0YSwgb3B0aW9ucyA9IHt9KSB7XG4gICAgc3VwZXIobWVzc2FnZSwgb3B0aW9ucyksIHRoaXMuZGF0YSA9IGRhdGE7XG4gIH1cbn1cbmNsYXNzIE1lc3NhZ2VQYXJzZUVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBuYW1lID0gXCJNZXNzYWdlUGFyc2VFcnJvclwiO1xufVxuY29uc3QgUkVRVUlSRURfRVZFTlRTID0gW1wiY2hhbm5lbEVycm9yXCIsIFwiZGlzY29ubmVjdFwiXTtcbmZ1bmN0aW9uIGNvbm5lY3RFdmVudFNvdXJjZShpbml0RXZlbnRTb3VyY2UsIGV2ZW50cykge1xuICByZXR1cm4gZGVmZXIoKCkgPT4ge1xuICAgIGNvbnN0IGVzID0gaW5pdEV2ZW50U291cmNlKCk7XG4gICAgcmV0dXJuIGlzT2JzZXJ2YWJsZShlcykgPyBlcyA6IG9mKGVzKTtcbiAgfSkucGlwZShtZXJnZU1hcCgoZXMpID0+IGNvbm5lY3RXaXRoRVNJbnN0YW5jZShlcywgZXZlbnRzKSkpO1xufVxuZnVuY3Rpb24gY29ubmVjdFdpdGhFU0luc3RhbmNlKGVzLCBldmVudHMpIHtcbiAgcmV0dXJuIG5ldyBPYnNlcnZhYmxlKChvYnNlcnZlcikgPT4ge1xuICAgIGNvbnN0IGVtaXRPcGVuID0gZXZlbnRzLmluY2x1ZGVzKFwib3BlblwiKSwgZW1pdFJlY29ubmVjdCA9IGV2ZW50cy5pbmNsdWRlcyhcInJlY29ubmVjdFwiKTtcbiAgICBmdW5jdGlvbiBvbkVycm9yKGV2dCkge1xuICAgICAgaWYgKFwiZGF0YVwiIGluIGV2dCkge1xuICAgICAgICBjb25zdCBbcGFyc2VFcnJvciwgZXZlbnRdID0gcGFyc2VFdmVudChldnQpO1xuICAgICAgICBvYnNlcnZlci5lcnJvcihcbiAgICAgICAgICBwYXJzZUVycm9yID8gbmV3IE1lc3NhZ2VQYXJzZUVycm9yKFwiVW5hYmxlIHRvIHBhcnNlIEV2ZW50U291cmNlIGVycm9yIG1lc3NhZ2VcIiwgeyBjYXVzZTogZXZlbnQgfSkgOiBuZXcgTWVzc2FnZUVycm9yKChldmVudD8uZGF0YSkubWVzc2FnZSwgZXZlbnQpXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGVzLnJlYWR5U3RhdGUgPT09IGVzLkNMT1NFRCA/IG9ic2VydmVyLmVycm9yKG5ldyBDb25uZWN0aW9uRmFpbGVkRXJyb3IoXCJFdmVudFNvdXJjZSBjb25uZWN0aW9uIGZhaWxlZFwiKSkgOiBlbWl0UmVjb25uZWN0ICYmIG9ic2VydmVyLm5leHQoeyB0eXBlOiBcInJlY29ubmVjdFwiIH0pO1xuICAgIH1cbiAgICBmdW5jdGlvbiBvbk9wZW4oKSB7XG4gICAgICBvYnNlcnZlci5uZXh0KHsgdHlwZTogXCJvcGVuXCIgfSk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIG9uTWVzc2FnZShtZXNzYWdlKSB7XG4gICAgICBjb25zdCBbcGFyc2VFcnJvciwgZXZlbnRdID0gcGFyc2VFdmVudChtZXNzYWdlKTtcbiAgICAgIGlmIChwYXJzZUVycm9yKSB7XG4gICAgICAgIG9ic2VydmVyLmVycm9yKFxuICAgICAgICAgIG5ldyBNZXNzYWdlUGFyc2VFcnJvcihcIlVuYWJsZSB0byBwYXJzZSBFdmVudFNvdXJjZSBtZXNzYWdlXCIsIHsgY2F1c2U6IHBhcnNlRXJyb3IgfSlcbiAgICAgICAgKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgaWYgKG1lc3NhZ2UudHlwZSA9PT0gXCJjaGFubmVsRXJyb3JcIikge1xuICAgICAgICBvYnNlcnZlci5lcnJvcihuZXcgQ2hhbm5lbEVycm9yKGV4dHJhY3RFcnJvck1lc3NhZ2UoZXZlbnQ/LmRhdGEpLCBldmVudC5kYXRhKSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGlmIChtZXNzYWdlLnR5cGUgPT09IFwiZGlzY29ubmVjdFwiKSB7XG4gICAgICAgIG9ic2VydmVyLmVycm9yKFxuICAgICAgICAgIG5ldyBEaXNjb25uZWN0RXJyb3IoXG4gICAgICAgICAgICBgU2VydmVyIGRpc2Nvbm5lY3RlZCBjbGllbnQ6ICR7ZXZlbnQuZGF0YT8ucmVhc29uIHx8IFwidW5rbm93biBlcnJvclwifWBcbiAgICAgICAgICApXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIG9ic2VydmVyLm5leHQoe1xuICAgICAgICB0eXBlOiBtZXNzYWdlLnR5cGUsXG4gICAgICAgIGlkOiBtZXNzYWdlLmxhc3RFdmVudElkLFxuICAgICAgICAuLi5ldmVudC5kYXRhID8geyBkYXRhOiBldmVudC5kYXRhIH0gOiB7fVxuICAgICAgfSk7XG4gICAgfVxuICAgIGVzLmFkZEV2ZW50TGlzdGVuZXIoXCJlcnJvclwiLCBvbkVycm9yKSwgZW1pdE9wZW4gJiYgZXMuYWRkRXZlbnRMaXN0ZW5lcihcIm9wZW5cIiwgb25PcGVuKTtcbiAgICBjb25zdCBjbGVhbmVkRXZlbnRzID0gWy4uLi8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KFsuLi5SRVFVSVJFRF9FVkVOVFMsIC4uLmV2ZW50c10pXS5maWx0ZXIoKHR5cGUpID0+IHR5cGUgIT09IFwiZXJyb3JcIiAmJiB0eXBlICE9PSBcIm9wZW5cIiAmJiB0eXBlICE9PSBcInJlY29ubmVjdFwiKTtcbiAgICByZXR1cm4gY2xlYW5lZEV2ZW50cy5mb3JFYWNoKCh0eXBlKSA9PiBlcy5hZGRFdmVudExpc3RlbmVyKHR5cGUsIG9uTWVzc2FnZSkpLCAoKSA9PiB7XG4gICAgICBlcy5yZW1vdmVFdmVudExpc3RlbmVyKFwiZXJyb3JcIiwgb25FcnJvciksIGVtaXRPcGVuICYmIGVzLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvcGVuXCIsIG9uT3BlbiksIGNsZWFuZWRFdmVudHMuZm9yRWFjaCgodHlwZSkgPT4gZXMucmVtb3ZlRXZlbnRMaXN0ZW5lcih0eXBlLCBvbk1lc3NhZ2UpKSwgZXMuY2xvc2UoKTtcbiAgICB9O1xuICB9KTtcbn1cbmZ1bmN0aW9uIHBhcnNlRXZlbnQobWVzc2FnZSkge1xuICB0cnkge1xuICAgIGNvbnN0IGRhdGEgPSB0eXBlb2YgbWVzc2FnZS5kYXRhID09IFwic3RyaW5nXCIgJiYgSlNPTi5wYXJzZShtZXNzYWdlLmRhdGEpO1xuICAgIHJldHVybiBbXG4gICAgICBudWxsLFxuICAgICAge1xuICAgICAgICB0eXBlOiBtZXNzYWdlLnR5cGUsXG4gICAgICAgIGlkOiBtZXNzYWdlLmxhc3RFdmVudElkLFxuICAgICAgICAuLi5pc0VtcHR5T2JqZWN0KGRhdGEpID8ge30gOiB7IGRhdGEgfVxuICAgICAgfVxuICAgIF07XG4gIH0gY2F0Y2ggKGVycikge1xuICAgIHJldHVybiBbZXJyLCBudWxsXTtcbiAgfVxufVxuZnVuY3Rpb24gZXh0cmFjdEVycm9yTWVzc2FnZShlcnIpIHtcbiAgcmV0dXJuIGVyci5lcnJvciA/IGVyci5lcnJvci5kZXNjcmlwdGlvbiA/IGVyci5lcnJvci5kZXNjcmlwdGlvbiA6IHR5cGVvZiBlcnIuZXJyb3IgPT0gXCJzdHJpbmdcIiA/IGVyci5lcnJvciA6IEpTT04uc3RyaW5naWZ5KGVyci5lcnJvciwgbnVsbCwgMikgOiBlcnIubWVzc2FnZSB8fCBcIlVua25vd24gbGlzdGVuZXIgZXJyb3JcIjtcbn1cbmZ1bmN0aW9uIGlzRW1wdHlPYmplY3QoZGF0YSkge1xuICBmb3IgKGNvbnN0IF8gaW4gZGF0YSlcbiAgICByZXR1cm4gITE7XG4gIHJldHVybiAhMDtcbn1cbmZ1bmN0aW9uIGdldFNlbGVjdGlvbihzZWwpIHtcbiAgaWYgKHR5cGVvZiBzZWwgPT0gXCJzdHJpbmdcIilcbiAgICByZXR1cm4geyBpZDogc2VsIH07XG4gIGlmIChBcnJheS5pc0FycmF5KHNlbCkpXG4gICAgcmV0dXJuIHsgcXVlcnk6IFwiKltfaWQgaW4gJGlkc11cIiwgcGFyYW1zOiB7IGlkczogc2VsIH0gfTtcbiAgaWYgKHR5cGVvZiBzZWwgPT0gXCJvYmplY3RcIiAmJiBzZWwgIT09IG51bGwgJiYgXCJxdWVyeVwiIGluIHNlbCAmJiB0eXBlb2Ygc2VsLnF1ZXJ5ID09IFwic3RyaW5nXCIpXG4gICAgcmV0dXJuIFwicGFyYW1zXCIgaW4gc2VsICYmIHR5cGVvZiBzZWwucGFyYW1zID09IFwib2JqZWN0XCIgJiYgc2VsLnBhcmFtcyAhPT0gbnVsbCA/IHsgcXVlcnk6IHNlbC5xdWVyeSwgcGFyYW1zOiBzZWwucGFyYW1zIH0gOiB7IHF1ZXJ5OiBzZWwucXVlcnkgfTtcbiAgY29uc3Qgc2VsZWN0aW9uT3B0cyA9IFtcbiAgICBcIiogRG9jdW1lbnQgSUQgKDxkb2NJZD4pXCIsXG4gICAgXCIqIEFycmF5IG9mIGRvY3VtZW50IElEc1wiLFxuICAgIFwiKiBPYmplY3QgY29udGFpbmluZyBgcXVlcnlgXCJcbiAgXS5qb2luKGBcbmApO1xuICB0aHJvdyBuZXcgRXJyb3IoYFVua25vd24gc2VsZWN0aW9uIC0gbXVzdCBiZSBvbmUgb2Y6XG5cbiR7c2VsZWN0aW9uT3B0c31gKTtcbn1cbmNsYXNzIEJhc2VQYXRjaCB7XG4gIHNlbGVjdGlvbjtcbiAgb3BlcmF0aW9ucztcbiAgY29uc3RydWN0b3Ioc2VsZWN0aW9uLCBvcGVyYXRpb25zID0ge30pIHtcbiAgICB0aGlzLnNlbGVjdGlvbiA9IHNlbGVjdGlvbiwgdGhpcy5vcGVyYXRpb25zID0gb3BlcmF0aW9ucztcbiAgfVxuICAvKipcbiAgICogU2V0cyB0aGUgZ2l2ZW4gYXR0cmlidXRlcyB0byB0aGUgZG9jdW1lbnQuIERvZXMgTk9UIG1lcmdlIG9iamVjdHMuXG4gICAqIFRoZSBvcGVyYXRpb24gaXMgYWRkZWQgdG8gdGhlIGN1cnJlbnQgcGF0Y2gsIHJlYWR5IHRvIGJlIGNvbW1pdGVkIGJ5IGBjb21taXQoKWBcbiAgICpcbiAgICogQHBhcmFtIGF0dHJzIC0gQXR0cmlidXRlcyB0byBzZXQuIFRvIHNldCBhIGRlZXAgYXR0cmlidXRlLCB1c2UgSlNPTk1hdGNoLCBlZzogXFx7XCJuZXN0ZWQucHJvcFwiOiBcInZhbHVlXCJcXH1cbiAgICovXG4gIHNldChhdHRycykge1xuICAgIHJldHVybiB0aGlzLl9hc3NpZ24oXCJzZXRcIiwgYXR0cnMpO1xuICB9XG4gIC8qKlxuICAgKiBTZXRzIHRoZSBnaXZlbiBhdHRyaWJ1dGVzIHRvIHRoZSBkb2N1bWVudCBpZiB0aGV5IGFyZSBub3QgY3VycmVudGx5IHNldC4gRG9lcyBOT1QgbWVyZ2Ugb2JqZWN0cy5cbiAgICogVGhlIG9wZXJhdGlvbiBpcyBhZGRlZCB0byB0aGUgY3VycmVudCBwYXRjaCwgcmVhZHkgdG8gYmUgY29tbWl0ZWQgYnkgYGNvbW1pdCgpYFxuICAgKlxuICAgKiBAcGFyYW0gYXR0cnMgLSBBdHRyaWJ1dGVzIHRvIHNldC4gVG8gc2V0IGEgZGVlcCBhdHRyaWJ1dGUsIHVzZSBKU09OTWF0Y2gsIGVnOiBcXHtcIm5lc3RlZC5wcm9wXCI6IFwidmFsdWVcIlxcfVxuICAgKi9cbiAgc2V0SWZNaXNzaW5nKGF0dHJzKSB7XG4gICAgcmV0dXJuIHRoaXMuX2Fzc2lnbihcInNldElmTWlzc2luZ1wiLCBhdHRycyk7XG4gIH1cbiAgLyoqXG4gICAqIFBlcmZvcm1zIGEgXCJkaWZmLW1hdGNoLXBhdGNoXCIgb3BlcmF0aW9uIG9uIHRoZSBzdHJpbmcgYXR0cmlidXRlcyBwcm92aWRlZC5cbiAgICogVGhlIG9wZXJhdGlvbiBpcyBhZGRlZCB0byB0aGUgY3VycmVudCBwYXRjaCwgcmVhZHkgdG8gYmUgY29tbWl0ZWQgYnkgYGNvbW1pdCgpYFxuICAgKlxuICAgKiBAcGFyYW0gYXR0cnMgLSBBdHRyaWJ1dGVzIHRvIHBlcmZvcm0gb3BlcmF0aW9uIG9uLiBUbyBzZXQgYSBkZWVwIGF0dHJpYnV0ZSwgdXNlIEpTT05NYXRjaCwgZWc6IFxce1wibmVzdGVkLnByb3BcIjogXCJkbXBcIlxcfVxuICAgKi9cbiAgZGlmZk1hdGNoUGF0Y2goYXR0cnMpIHtcbiAgICByZXR1cm4gdmFsaWRhdGVPYmplY3QoXCJkaWZmTWF0Y2hQYXRjaFwiLCBhdHRycyksIHRoaXMuX2Fzc2lnbihcImRpZmZNYXRjaFBhdGNoXCIsIGF0dHJzKTtcbiAgfVxuICAvKipcbiAgICogVW5zZXRzIHRoZSBhdHRyaWJ1dGUgcGF0aHMgcHJvdmlkZWQuXG4gICAqIFRoZSBvcGVyYXRpb24gaXMgYWRkZWQgdG8gdGhlIGN1cnJlbnQgcGF0Y2gsIHJlYWR5IHRvIGJlIGNvbW1pdGVkIGJ5IGBjb21taXQoKWBcbiAgICpcbiAgICogQHBhcmFtIGF0dHJzIC0gQXR0cmlidXRlIHBhdGhzIHRvIHVuc2V0LlxuICAgKi9cbiAgdW5zZXQoYXR0cnMpIHtcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkoYXR0cnMpKVxuICAgICAgdGhyb3cgbmV3IEVycm9yKFwidW5zZXQoYXR0cnMpIHRha2VzIGFuIGFycmF5IG9mIGF0dHJpYnV0ZXMgdG8gdW5zZXQsIG5vbi1hcnJheSBnaXZlblwiKTtcbiAgICByZXR1cm4gdGhpcy5vcGVyYXRpb25zID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5vcGVyYXRpb25zLCB7IHVuc2V0OiBhdHRycyB9KSwgdGhpcztcbiAgfVxuICAvKipcbiAgICogSW5jcmVtZW50IGEgbnVtZXJpYyB2YWx1ZS4gRWFjaCBlbnRyeSBpbiB0aGUgYXJndW1lbnQgaXMgZWl0aGVyIGFuIGF0dHJpYnV0ZSBvciBhIEpTT04gcGF0aC4gVGhlIHZhbHVlIG1heSBiZSBhIHBvc2l0aXZlIG9yIG5lZ2F0aXZlIGludGVnZXIgb3IgZmxvYXRpbmctcG9pbnQgdmFsdWUuIFRoZSBvcGVyYXRpb24gd2lsbCBmYWlsIGlmIHRhcmdldCB2YWx1ZSBpcyBub3QgYSBudW1lcmljIHZhbHVlLCBvciBkb2Vzbid0IGV4aXN0LlxuICAgKlxuICAgKiBAcGFyYW0gYXR0cnMgLSBPYmplY3Qgb2YgYXR0cmlidXRlIHBhdGhzIHRvIGluY3JlbWVudCwgdmFsdWVzIHJlcHJlc2VudGluZyB0aGUgbnVtYmVyIHRvIGluY3JlbWVudCBieS5cbiAgICovXG4gIGluYyhhdHRycykge1xuICAgIHJldHVybiB0aGlzLl9hc3NpZ24oXCJpbmNcIiwgYXR0cnMpO1xuICB9XG4gIC8qKlxuICAgKiBEZWNyZW1lbnQgYSBudW1lcmljIHZhbHVlLiBFYWNoIGVudHJ5IGluIHRoZSBhcmd1bWVudCBpcyBlaXRoZXIgYW4gYXR0cmlidXRlIG9yIGEgSlNPTiBwYXRoLiBUaGUgdmFsdWUgbWF5IGJlIGEgcG9zaXRpdmUgb3IgbmVnYXRpdmUgaW50ZWdlciBvciBmbG9hdGluZy1wb2ludCB2YWx1ZS4gVGhlIG9wZXJhdGlvbiB3aWxsIGZhaWwgaWYgdGFyZ2V0IHZhbHVlIGlzIG5vdCBhIG51bWVyaWMgdmFsdWUsIG9yIGRvZXNuJ3QgZXhpc3QuXG4gICAqXG4gICAqIEBwYXJhbSBhdHRycyAtIE9iamVjdCBvZiBhdHRyaWJ1dGUgcGF0aHMgdG8gZGVjcmVtZW50LCB2YWx1ZXMgcmVwcmVzZW50aW5nIHRoZSBudW1iZXIgdG8gZGVjcmVtZW50IGJ5LlxuICAgKi9cbiAgZGVjKGF0dHJzKSB7XG4gICAgcmV0dXJuIHRoaXMuX2Fzc2lnbihcImRlY1wiLCBhdHRycyk7XG4gIH1cbiAgLyoqXG4gICAqIFByb3ZpZGVzIG1ldGhvZHMgZm9yIG1vZGlmeWluZyBhcnJheXMsIGJ5IGluc2VydGluZywgYXBwZW5kaW5nIGFuZCByZXBsYWNpbmcgZWxlbWVudHMgdmlhIGEgSlNPTlBhdGggZXhwcmVzc2lvbi5cbiAgICpcbiAgICogQHBhcmFtIGF0IC0gTG9jYXRpb24gdG8gaW5zZXJ0IGF0LCByZWxhdGl2ZSB0byB0aGUgZ2l2ZW4gc2VsZWN0b3IsIG9yICdyZXBsYWNlJyB0aGUgbWF0Y2hlZCBwYXRoXG4gICAqIEBwYXJhbSBzZWxlY3RvciAtIEpTT05QYXRoIGV4cHJlc3Npb24sIGVnIGBjb21tZW50c1stMV1gIG9yIGBibG9ja3NbX2tleT09XCJhYmMxMjNcIl1gXG4gICAqIEBwYXJhbSBpdGVtcyAtIEFycmF5IG9mIGl0ZW1zIHRvIGluc2VydC9yZXBsYWNlXG4gICAqL1xuICBpbnNlcnQoYXQsIHNlbGVjdG9yLCBpdGVtcykge1xuICAgIHJldHVybiB2YWxpZGF0ZUluc2VydChhdCwgc2VsZWN0b3IsIGl0ZW1zKSwgdGhpcy5fYXNzaWduKFwiaW5zZXJ0XCIsIHsgW2F0XTogc2VsZWN0b3IsIGl0ZW1zIH0pO1xuICB9XG4gIC8qKlxuICAgKiBBcHBlbmQgdGhlIGdpdmVuIGl0ZW1zIHRvIHRoZSBhcnJheSBhdCB0aGUgZ2l2ZW4gSlNPTlBhdGhcbiAgICpcbiAgICogQHBhcmFtIHNlbGVjdG9yIC0gQXR0cmlidXRlL3BhdGggdG8gYXBwZW5kIHRvLCBlZyBgY29tbWVudHNgIG9yIGBwZXJzb24uaG9iYmllc2BcbiAgICogQHBhcmFtIGl0ZW1zIC0gQXJyYXkgb2YgaXRlbXMgdG8gYXBwZW5kIHRvIHRoZSBhcnJheVxuICAgKi9cbiAgYXBwZW5kKHNlbGVjdG9yLCBpdGVtcykge1xuICAgIHJldHVybiB0aGlzLmluc2VydChcImFmdGVyXCIsIGAke3NlbGVjdG9yfVstMV1gLCBpdGVtcyk7XG4gIH1cbiAgLyoqXG4gICAqIFByZXBlbmQgdGhlIGdpdmVuIGl0ZW1zIHRvIHRoZSBhcnJheSBhdCB0aGUgZ2l2ZW4gSlNPTlBhdGhcbiAgICpcbiAgICogQHBhcmFtIHNlbGVjdG9yIC0gQXR0cmlidXRlL3BhdGggdG8gcHJlcGVuZCB0bywgZWcgYGNvbW1lbnRzYCBvciBgcGVyc29uLmhvYmJpZXNgXG4gICAqIEBwYXJhbSBpdGVtcyAtIEFycmF5IG9mIGl0ZW1zIHRvIHByZXBlbmQgdG8gdGhlIGFycmF5XG4gICAqL1xuICBwcmVwZW5kKHNlbGVjdG9yLCBpdGVtcykge1xuICAgIHJldHVybiB0aGlzLmluc2VydChcImJlZm9yZVwiLCBgJHtzZWxlY3Rvcn1bMF1gLCBpdGVtcyk7XG4gIH1cbiAgLyoqXG4gICAqIENoYW5nZSB0aGUgY29udGVudHMgb2YgYW4gYXJyYXkgYnkgcmVtb3ZpbmcgZXhpc3RpbmcgZWxlbWVudHMgYW5kL29yIGFkZGluZyBuZXcgZWxlbWVudHMuXG4gICAqXG4gICAqIEBwYXJhbSBzZWxlY3RvciAtIEF0dHJpYnV0ZSBvciBKU09OUGF0aCBleHByZXNzaW9uIGZvciBhcnJheVxuICAgKiBAcGFyYW0gc3RhcnQgLSBJbmRleCBhdCB3aGljaCB0byBzdGFydCBjaGFuZ2luZyB0aGUgYXJyYXkgKHdpdGggb3JpZ2luIDApLiBJZiBncmVhdGVyIHRoYW4gdGhlIGxlbmd0aCBvZiB0aGUgYXJyYXksIGFjdHVhbCBzdGFydGluZyBpbmRleCB3aWxsIGJlIHNldCB0byB0aGUgbGVuZ3RoIG9mIHRoZSBhcnJheS4gSWYgbmVnYXRpdmUsIHdpbGwgYmVnaW4gdGhhdCBtYW55IGVsZW1lbnRzIGZyb20gdGhlIGVuZCBvZiB0aGUgYXJyYXkgKHdpdGggb3JpZ2luIC0xKSBhbmQgd2lsbCBiZSBzZXQgdG8gMCBpZiBhYnNvbHV0ZSB2YWx1ZSBpcyBncmVhdGVyIHRoYW4gdGhlIGxlbmd0aCBvZiB0aGUgYXJyYXkueFxuICAgKiBAcGFyYW0gZGVsZXRlQ291bnQgLSBBbiBpbnRlZ2VyIGluZGljYXRpbmcgdGhlIG51bWJlciBvZiBvbGQgYXJyYXkgZWxlbWVudHMgdG8gcmVtb3ZlLlxuICAgKiBAcGFyYW0gaXRlbXMgLSBUaGUgZWxlbWVudHMgdG8gYWRkIHRvIHRoZSBhcnJheSwgYmVnaW5uaW5nIGF0IHRoZSBzdGFydCBpbmRleC4gSWYgeW91IGRvbid0IHNwZWNpZnkgYW55IGVsZW1lbnRzLCBzcGxpY2UoKSB3aWxsIG9ubHkgcmVtb3ZlIGVsZW1lbnRzIGZyb20gdGhlIGFycmF5LlxuICAgKi9cbiAgc3BsaWNlKHNlbGVjdG9yLCBzdGFydCwgZGVsZXRlQ291bnQsIGl0ZW1zKSB7XG4gICAgY29uc3QgZGVsQWxsID0gdHlwZW9mIGRlbGV0ZUNvdW50ID4gXCJ1XCIgfHwgZGVsZXRlQ291bnQgPT09IC0xLCBzdGFydEluZGV4ID0gc3RhcnQgPCAwID8gc3RhcnQgLSAxIDogc3RhcnQsIGRlbENvdW50ID0gZGVsQWxsID8gLTEgOiBNYXRoLm1heCgwLCBzdGFydCArIGRlbGV0ZUNvdW50KSwgZGVsUmFuZ2UgPSBzdGFydEluZGV4IDwgMCAmJiBkZWxDb3VudCA+PSAwID8gXCJcIiA6IGRlbENvdW50LCByYW5nZVNlbGVjdG9yID0gYCR7c2VsZWN0b3J9WyR7c3RhcnRJbmRleH06JHtkZWxSYW5nZX1dYDtcbiAgICByZXR1cm4gdGhpcy5pbnNlcnQoXCJyZXBsYWNlXCIsIHJhbmdlU2VsZWN0b3IsIGl0ZW1zIHx8IFtdKTtcbiAgfVxuICAvKipcbiAgICogQWRkcyBhIHJldmlzaW9uIGNsYXVzZSwgcHJldmVudGluZyB0aGUgZG9jdW1lbnQgZnJvbSBiZWluZyBwYXRjaGVkIGlmIHRoZSBgX3JldmAgcHJvcGVydHkgZG9lcyBub3QgbWF0Y2ggdGhlIGdpdmVuIHZhbHVlXG4gICAqXG4gICAqIEBwYXJhbSByZXYgLSBSZXZpc2lvbiB0byBsb2NrIHRoZSBwYXRjaCB0b1xuICAgKi9cbiAgaWZSZXZpc2lvbklkKHJldikge1xuICAgIHJldHVybiB0aGlzLm9wZXJhdGlvbnMuaWZSZXZpc2lvbklEID0gcmV2LCB0aGlzO1xuICB9XG4gIC8qKlxuICAgKiBSZXR1cm4gYSBwbGFpbiBKU09OIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBwYXRjaFxuICAgKi9cbiAgc2VyaWFsaXplKCkge1xuICAgIHJldHVybiB7IC4uLmdldFNlbGVjdGlvbih0aGlzLnNlbGVjdGlvbiksIC4uLnRoaXMub3BlcmF0aW9ucyB9O1xuICB9XG4gIC8qKlxuICAgKiBSZXR1cm4gYSBwbGFpbiBKU09OIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBwYXRjaFxuICAgKi9cbiAgdG9KU09OKCkge1xuICAgIHJldHVybiB0aGlzLnNlcmlhbGl6ZSgpO1xuICB9XG4gIC8qKlxuICAgKiBDbGVhcnMgdGhlIHBhdGNoIG9mIGFsbCBvcGVyYXRpb25zXG4gICAqL1xuICByZXNldCgpIHtcbiAgICByZXR1cm4gdGhpcy5vcGVyYXRpb25zID0ge30sIHRoaXM7XG4gIH1cbiAgX2Fzc2lnbihvcCwgcHJvcHMsIG1lcmdlMiA9ICEwKSB7XG4gICAgcmV0dXJuIHZhbGlkYXRlT2JqZWN0KG9wLCBwcm9wcyksIHRoaXMub3BlcmF0aW9ucyA9IE9iamVjdC5hc3NpZ24oe30sIHRoaXMub3BlcmF0aW9ucywge1xuICAgICAgW29wXTogT2JqZWN0LmFzc2lnbih7fSwgbWVyZ2UyICYmIHRoaXMub3BlcmF0aW9uc1tvcF0gfHwge30sIHByb3BzKVxuICAgIH0pLCB0aGlzO1xuICB9XG4gIF9zZXQob3AsIHByb3BzKSB7XG4gICAgcmV0dXJuIHRoaXMuX2Fzc2lnbihvcCwgcHJvcHMsICExKTtcbiAgfVxufVxuY2xhc3MgT2JzZXJ2YWJsZVBhdGNoIGV4dGVuZHMgQmFzZVBhdGNoIHtcbiAgI2NsaWVudDtcbiAgY29uc3RydWN0b3Ioc2VsZWN0aW9uLCBvcGVyYXRpb25zLCBjbGllbnQpIHtcbiAgICBzdXBlcihzZWxlY3Rpb24sIG9wZXJhdGlvbnMpLCB0aGlzLiNjbGllbnQgPSBjbGllbnQ7XG4gIH1cbiAgLyoqXG4gICAqIENsb25lcyB0aGUgcGF0Y2hcbiAgICovXG4gIGNsb25lKCkge1xuICAgIHJldHVybiBuZXcgT2JzZXJ2YWJsZVBhdGNoKHRoaXMuc2VsZWN0aW9uLCB7IC4uLnRoaXMub3BlcmF0aW9ucyB9LCB0aGlzLiNjbGllbnQpO1xuICB9XG4gIGNvbW1pdChvcHRpb25zKSB7XG4gICAgaWYgKCF0aGlzLiNjbGllbnQpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIFwiTm8gYGNsaWVudGAgcGFzc2VkIHRvIHBhdGNoLCBlaXRoZXIgcHJvdmlkZSBvbmUgb3IgcGFzcyB0aGUgcGF0Y2ggdG8gYSBjbGllbnRzIGBtdXRhdGUoKWAgbWV0aG9kXCJcbiAgICAgICk7XG4gICAgY29uc3QgcmV0dXJuRmlyc3QgPSB0eXBlb2YgdGhpcy5zZWxlY3Rpb24gPT0gXCJzdHJpbmdcIiwgb3B0cyA9IE9iamVjdC5hc3NpZ24oeyByZXR1cm5GaXJzdCwgcmV0dXJuRG9jdW1lbnRzOiAhMCB9LCBvcHRpb25zKTtcbiAgICByZXR1cm4gdGhpcy4jY2xpZW50Lm11dGF0ZSh7IHBhdGNoOiB0aGlzLnNlcmlhbGl6ZSgpIH0sIG9wdHMpO1xuICB9XG59XG5jbGFzcyBQYXRjaCBleHRlbmRzIEJhc2VQYXRjaCB7XG4gICNjbGllbnQ7XG4gIGNvbnN0cnVjdG9yKHNlbGVjdGlvbiwgb3BlcmF0aW9ucywgY2xpZW50KSB7XG4gICAgc3VwZXIoc2VsZWN0aW9uLCBvcGVyYXRpb25zKSwgdGhpcy4jY2xpZW50ID0gY2xpZW50O1xuICB9XG4gIC8qKlxuICAgKiBDbG9uZXMgdGhlIHBhdGNoXG4gICAqL1xuICBjbG9uZSgpIHtcbiAgICByZXR1cm4gbmV3IFBhdGNoKHRoaXMuc2VsZWN0aW9uLCB7IC4uLnRoaXMub3BlcmF0aW9ucyB9LCB0aGlzLiNjbGllbnQpO1xuICB9XG4gIGNvbW1pdChvcHRpb25zKSB7XG4gICAgaWYgKCF0aGlzLiNjbGllbnQpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIFwiTm8gYGNsaWVudGAgcGFzc2VkIHRvIHBhdGNoLCBlaXRoZXIgcHJvdmlkZSBvbmUgb3IgcGFzcyB0aGUgcGF0Y2ggdG8gYSBjbGllbnRzIGBtdXRhdGUoKWAgbWV0aG9kXCJcbiAgICAgICk7XG4gICAgY29uc3QgcmV0dXJuRmlyc3QgPSB0eXBlb2YgdGhpcy5zZWxlY3Rpb24gPT0gXCJzdHJpbmdcIiwgb3B0cyA9IE9iamVjdC5hc3NpZ24oeyByZXR1cm5GaXJzdCwgcmV0dXJuRG9jdW1lbnRzOiAhMCB9LCBvcHRpb25zKTtcbiAgICByZXR1cm4gdGhpcy4jY2xpZW50Lm11dGF0ZSh7IHBhdGNoOiB0aGlzLnNlcmlhbGl6ZSgpIH0sIG9wdHMpO1xuICB9XG59XG5jb25zdCBkZWZhdWx0TXV0YXRlT3B0aW9ucyA9IHsgcmV0dXJuRG9jdW1lbnRzOiAhMSB9O1xuY2xhc3MgQmFzZVRyYW5zYWN0aW9uIHtcbiAgb3BlcmF0aW9ucztcbiAgdHJ4SWQ7XG4gIGNvbnN0cnVjdG9yKG9wZXJhdGlvbnMgPSBbXSwgdHJhbnNhY3Rpb25JZCkge1xuICAgIHRoaXMub3BlcmF0aW9ucyA9IG9wZXJhdGlvbnMsIHRoaXMudHJ4SWQgPSB0cmFuc2FjdGlvbklkO1xuICB9XG4gIC8qKlxuICAgKiBDcmVhdGVzIGEgbmV3IFNhbml0eSBkb2N1bWVudC4gSWYgYF9pZGAgaXMgcHJvdmlkZWQgYW5kIGFscmVhZHkgZXhpc3RzLCB0aGUgbXV0YXRpb24gd2lsbCBmYWlsLiBJZiBubyBgX2lkYCBpcyBnaXZlbiwgb25lIHdpbGwgYXV0b21hdGljYWxseSBiZSBnZW5lcmF0ZWQgYnkgdGhlIGRhdGFiYXNlLlxuICAgKiBUaGUgb3BlcmF0aW9uIGlzIGFkZGVkIHRvIHRoZSBjdXJyZW50IHRyYW5zYWN0aW9uLCByZWFkeSB0byBiZSBjb21taXRlZCBieSBgY29tbWl0KClgXG4gICAqXG4gICAqIEBwYXJhbSBkb2MgLSBEb2N1bWVudCB0byBjcmVhdGUuIFJlcXVpcmVzIGEgYF90eXBlYCBwcm9wZXJ0eS5cbiAgICovXG4gIGNyZWF0ZShkb2MpIHtcbiAgICByZXR1cm4gdmFsaWRhdGVPYmplY3QoXCJjcmVhdGVcIiwgZG9jKSwgdGhpcy5fYWRkKHsgY3JlYXRlOiBkb2MgfSk7XG4gIH1cbiAgLyoqXG4gICAqIENyZWF0ZXMgYSBuZXcgU2FuaXR5IGRvY3VtZW50LiBJZiBhIGRvY3VtZW50IHdpdGggdGhlIHNhbWUgYF9pZGAgYWxyZWFkeSBleGlzdHMsIHRoZSBjcmVhdGUgb3BlcmF0aW9uIHdpbGwgYmUgaWdub3JlZC5cbiAgICogVGhlIG9wZXJhdGlvbiBpcyBhZGRlZCB0byB0aGUgY3VycmVudCB0cmFuc2FjdGlvbiwgcmVhZHkgdG8gYmUgY29tbWl0ZWQgYnkgYGNvbW1pdCgpYFxuICAgKlxuICAgKiBAcGFyYW0gZG9jIC0gRG9jdW1lbnQgdG8gY3JlYXRlIGlmIGl0IGRvZXMgbm90IGFscmVhZHkgZXhpc3QuIFJlcXVpcmVzIGBfaWRgIGFuZCBgX3R5cGVgIHByb3BlcnRpZXMuXG4gICAqL1xuICBjcmVhdGVJZk5vdEV4aXN0cyhkb2MpIHtcbiAgICBjb25zdCBvcCA9IFwiY3JlYXRlSWZOb3RFeGlzdHNcIjtcbiAgICByZXR1cm4gdmFsaWRhdGVPYmplY3Qob3AsIGRvYyksIHJlcXVpcmVEb2N1bWVudElkKG9wLCBkb2MpLCB0aGlzLl9hZGQoeyBbb3BdOiBkb2MgfSk7XG4gIH1cbiAgLyoqXG4gICAqIENyZWF0ZXMgYSBuZXcgU2FuaXR5IGRvY3VtZW50LCBvciByZXBsYWNlcyBhbiBleGlzdGluZyBvbmUgaWYgdGhlIHNhbWUgYF9pZGAgaXMgYWxyZWFkeSB1c2VkLlxuICAgKiBUaGUgb3BlcmF0aW9uIGlzIGFkZGVkIHRvIHRoZSBjdXJyZW50IHRyYW5zYWN0aW9uLCByZWFkeSB0byBiZSBjb21taXRlZCBieSBgY29tbWl0KClgXG4gICAqXG4gICAqIEBwYXJhbSBkb2MgLSBEb2N1bWVudCB0byBjcmVhdGUgb3IgcmVwbGFjZS4gUmVxdWlyZXMgYF9pZGAgYW5kIGBfdHlwZWAgcHJvcGVydGllcy5cbiAgICovXG4gIGNyZWF0ZU9yUmVwbGFjZShkb2MpIHtcbiAgICBjb25zdCBvcCA9IFwiY3JlYXRlT3JSZXBsYWNlXCI7XG4gICAgcmV0dXJuIHZhbGlkYXRlT2JqZWN0KG9wLCBkb2MpLCByZXF1aXJlRG9jdW1lbnRJZChvcCwgZG9jKSwgdGhpcy5fYWRkKHsgW29wXTogZG9jIH0pO1xuICB9XG4gIC8qKlxuICAgKiBEZWxldGVzIHRoZSBkb2N1bWVudCB3aXRoIHRoZSBnaXZlbiBkb2N1bWVudCBJRFxuICAgKiBUaGUgb3BlcmF0aW9uIGlzIGFkZGVkIHRvIHRoZSBjdXJyZW50IHRyYW5zYWN0aW9uLCByZWFkeSB0byBiZSBjb21taXRlZCBieSBgY29tbWl0KClgXG4gICAqXG4gICAqIEBwYXJhbSBkb2N1bWVudElkIC0gRG9jdW1lbnQgSUQgdG8gZGVsZXRlXG4gICAqL1xuICBkZWxldGUoZG9jdW1lbnRJZCkge1xuICAgIHJldHVybiB2YWxpZGF0ZURvY3VtZW50SWQoXCJkZWxldGVcIiwgZG9jdW1lbnRJZCksIHRoaXMuX2FkZCh7IGRlbGV0ZTogeyBpZDogZG9jdW1lbnRJZCB9IH0pO1xuICB9XG4gIHRyYW5zYWN0aW9uSWQoaWQpIHtcbiAgICByZXR1cm4gaWQgPyAodGhpcy50cnhJZCA9IGlkLCB0aGlzKSA6IHRoaXMudHJ4SWQ7XG4gIH1cbiAgLyoqXG4gICAqIFJldHVybiBhIHBsYWluIEpTT04gcmVwcmVzZW50YXRpb24gb2YgdGhlIHRyYW5zYWN0aW9uXG4gICAqL1xuICBzZXJpYWxpemUoKSB7XG4gICAgcmV0dXJuIFsuLi50aGlzLm9wZXJhdGlvbnNdO1xuICB9XG4gIC8qKlxuICAgKiBSZXR1cm4gYSBwbGFpbiBKU09OIHJlcHJlc2VudGF0aW9uIG9mIHRoZSB0cmFuc2FjdGlvblxuICAgKi9cbiAgdG9KU09OKCkge1xuICAgIHJldHVybiB0aGlzLnNlcmlhbGl6ZSgpO1xuICB9XG4gIC8qKlxuICAgKiBDbGVhcnMgdGhlIHRyYW5zYWN0aW9uIG9mIGFsbCBvcGVyYXRpb25zXG4gICAqL1xuICByZXNldCgpIHtcbiAgICByZXR1cm4gdGhpcy5vcGVyYXRpb25zID0gW10sIHRoaXM7XG4gIH1cbiAgX2FkZChtdXQpIHtcbiAgICByZXR1cm4gdGhpcy5vcGVyYXRpb25zLnB1c2gobXV0KSwgdGhpcztcbiAgfVxufVxuY2xhc3MgVHJhbnNhY3Rpb24gZXh0ZW5kcyBCYXNlVHJhbnNhY3Rpb24ge1xuICAjY2xpZW50O1xuICBjb25zdHJ1Y3RvcihvcGVyYXRpb25zLCBjbGllbnQsIHRyYW5zYWN0aW9uSWQpIHtcbiAgICBzdXBlcihvcGVyYXRpb25zLCB0cmFuc2FjdGlvbklkKSwgdGhpcy4jY2xpZW50ID0gY2xpZW50O1xuICB9XG4gIC8qKlxuICAgKiBDbG9uZXMgdGhlIHRyYW5zYWN0aW9uXG4gICAqL1xuICBjbG9uZSgpIHtcbiAgICByZXR1cm4gbmV3IFRyYW5zYWN0aW9uKFsuLi50aGlzLm9wZXJhdGlvbnNdLCB0aGlzLiNjbGllbnQsIHRoaXMudHJ4SWQpO1xuICB9XG4gIGNvbW1pdChvcHRpb25zKSB7XG4gICAgaWYgKCF0aGlzLiNjbGllbnQpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIFwiTm8gYGNsaWVudGAgcGFzc2VkIHRvIHRyYW5zYWN0aW9uLCBlaXRoZXIgcHJvdmlkZSBvbmUgb3IgcGFzcyB0aGUgdHJhbnNhY3Rpb24gdG8gYSBjbGllbnRzIGBtdXRhdGUoKWAgbWV0aG9kXCJcbiAgICAgICk7XG4gICAgcmV0dXJuIHRoaXMuI2NsaWVudC5tdXRhdGUoXG4gICAgICB0aGlzLnNlcmlhbGl6ZSgpLFxuICAgICAgT2JqZWN0LmFzc2lnbih7IHRyYW5zYWN0aW9uSWQ6IHRoaXMudHJ4SWQgfSwgZGVmYXVsdE11dGF0ZU9wdGlvbnMsIG9wdGlvbnMgfHwge30pXG4gICAgKTtcbiAgfVxuICBwYXRjaChwYXRjaE9yRG9jdW1lbnRJZCwgcGF0Y2hPcHMpIHtcbiAgICBjb25zdCBpc0J1aWxkZXIgPSB0eXBlb2YgcGF0Y2hPcHMgPT0gXCJmdW5jdGlvblwiLCBpc1BhdGNoID0gdHlwZW9mIHBhdGNoT3JEb2N1bWVudElkICE9IFwic3RyaW5nXCIgJiYgcGF0Y2hPckRvY3VtZW50SWQgaW5zdGFuY2VvZiBQYXRjaCwgaXNNdXRhdGlvblNlbGVjdGlvbiA9IHR5cGVvZiBwYXRjaE9yRG9jdW1lbnRJZCA9PSBcIm9iamVjdFwiICYmIChcInF1ZXJ5XCIgaW4gcGF0Y2hPckRvY3VtZW50SWQgfHwgXCJpZFwiIGluIHBhdGNoT3JEb2N1bWVudElkKTtcbiAgICBpZiAoaXNQYXRjaClcbiAgICAgIHJldHVybiB0aGlzLl9hZGQoeyBwYXRjaDogcGF0Y2hPckRvY3VtZW50SWQuc2VyaWFsaXplKCkgfSk7XG4gICAgaWYgKGlzQnVpbGRlcikge1xuICAgICAgY29uc3QgcGF0Y2ggPSBwYXRjaE9wcyhuZXcgUGF0Y2gocGF0Y2hPckRvY3VtZW50SWQsIHt9LCB0aGlzLiNjbGllbnQpKTtcbiAgICAgIGlmICghKHBhdGNoIGluc3RhbmNlb2YgUGF0Y2gpKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJmdW5jdGlvbiBwYXNzZWQgdG8gYHBhdGNoKClgIG11c3QgcmV0dXJuIHRoZSBwYXRjaFwiKTtcbiAgICAgIHJldHVybiB0aGlzLl9hZGQoeyBwYXRjaDogcGF0Y2guc2VyaWFsaXplKCkgfSk7XG4gICAgfVxuICAgIGlmIChpc011dGF0aW9uU2VsZWN0aW9uKSB7XG4gICAgICBjb25zdCBwYXRjaCA9IG5ldyBQYXRjaChwYXRjaE9yRG9jdW1lbnRJZCwgcGF0Y2hPcHMgfHwge30sIHRoaXMuI2NsaWVudCk7XG4gICAgICByZXR1cm4gdGhpcy5fYWRkKHsgcGF0Y2g6IHBhdGNoLnNlcmlhbGl6ZSgpIH0pO1xuICAgIH1cbiAgICByZXR1cm4gdGhpcy5fYWRkKHsgcGF0Y2g6IHsgaWQ6IHBhdGNoT3JEb2N1bWVudElkLCAuLi5wYXRjaE9wcyB9IH0pO1xuICB9XG59XG5jbGFzcyBPYnNlcnZhYmxlVHJhbnNhY3Rpb24gZXh0ZW5kcyBCYXNlVHJhbnNhY3Rpb24ge1xuICAjY2xpZW50O1xuICBjb25zdHJ1Y3RvcihvcGVyYXRpb25zLCBjbGllbnQsIHRyYW5zYWN0aW9uSWQpIHtcbiAgICBzdXBlcihvcGVyYXRpb25zLCB0cmFuc2FjdGlvbklkKSwgdGhpcy4jY2xpZW50ID0gY2xpZW50O1xuICB9XG4gIC8qKlxuICAgKiBDbG9uZXMgdGhlIHRyYW5zYWN0aW9uXG4gICAqL1xuICBjbG9uZSgpIHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGVUcmFuc2FjdGlvbihbLi4udGhpcy5vcGVyYXRpb25zXSwgdGhpcy4jY2xpZW50LCB0aGlzLnRyeElkKTtcbiAgfVxuICBjb21taXQob3B0aW9ucykge1xuICAgIGlmICghdGhpcy4jY2xpZW50KVxuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBcIk5vIGBjbGllbnRgIHBhc3NlZCB0byB0cmFuc2FjdGlvbiwgZWl0aGVyIHByb3ZpZGUgb25lIG9yIHBhc3MgdGhlIHRyYW5zYWN0aW9uIHRvIGEgY2xpZW50cyBgbXV0YXRlKClgIG1ldGhvZFwiXG4gICAgICApO1xuICAgIHJldHVybiB0aGlzLiNjbGllbnQubXV0YXRlKFxuICAgICAgdGhpcy5zZXJpYWxpemUoKSxcbiAgICAgIE9iamVjdC5hc3NpZ24oeyB0cmFuc2FjdGlvbklkOiB0aGlzLnRyeElkIH0sIGRlZmF1bHRNdXRhdGVPcHRpb25zLCBvcHRpb25zIHx8IHt9KVxuICAgICk7XG4gIH1cbiAgcGF0Y2gocGF0Y2hPckRvY3VtZW50SWQsIHBhdGNoT3BzKSB7XG4gICAgY29uc3QgaXNCdWlsZGVyID0gdHlwZW9mIHBhdGNoT3BzID09IFwiZnVuY3Rpb25cIjtcbiAgICBpZiAodHlwZW9mIHBhdGNoT3JEb2N1bWVudElkICE9IFwic3RyaW5nXCIgJiYgcGF0Y2hPckRvY3VtZW50SWQgaW5zdGFuY2VvZiBPYnNlcnZhYmxlUGF0Y2gpXG4gICAgICByZXR1cm4gdGhpcy5fYWRkKHsgcGF0Y2g6IHBhdGNoT3JEb2N1bWVudElkLnNlcmlhbGl6ZSgpIH0pO1xuICAgIGlmIChpc0J1aWxkZXIpIHtcbiAgICAgIGNvbnN0IHBhdGNoID0gcGF0Y2hPcHMobmV3IE9ic2VydmFibGVQYXRjaChwYXRjaE9yRG9jdW1lbnRJZCwge30sIHRoaXMuI2NsaWVudCkpO1xuICAgICAgaWYgKCEocGF0Y2ggaW5zdGFuY2VvZiBPYnNlcnZhYmxlUGF0Y2gpKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJmdW5jdGlvbiBwYXNzZWQgdG8gYHBhdGNoKClgIG11c3QgcmV0dXJuIHRoZSBwYXRjaFwiKTtcbiAgICAgIHJldHVybiB0aGlzLl9hZGQoeyBwYXRjaDogcGF0Y2guc2VyaWFsaXplKCkgfSk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLl9hZGQoeyBwYXRjaDogeyBpZDogcGF0Y2hPckRvY3VtZW50SWQsIC4uLnBhdGNoT3BzIH0gfSk7XG4gIH1cbn1cbmNvbnN0IHByb2plY3RIZWFkZXIgPSBcIlgtU2FuaXR5LVByb2plY3QtSURcIjtcbmZ1bmN0aW9uIHJlcXVlc3RPcHRpb25zKGNvbmZpZywgb3ZlcnJpZGVzID0ge30pIHtcbiAgY29uc3QgaGVhZGVyczIgPSB7fSwgdG9rZW4gPSBvdmVycmlkZXMudG9rZW4gfHwgY29uZmlnLnRva2VuO1xuICB0b2tlbiAmJiAoaGVhZGVyczIuQXV0aG9yaXphdGlvbiA9IGBCZWFyZXIgJHt0b2tlbn1gKSwgIW92ZXJyaWRlcy51c2VHbG9iYWxBcGkgJiYgIWNvbmZpZy51c2VQcm9qZWN0SG9zdG5hbWUgJiYgY29uZmlnLnByb2plY3RJZCAmJiAoaGVhZGVyczJbcHJvamVjdEhlYWRlcl0gPSBjb25maWcucHJvamVjdElkKTtcbiAgY29uc3Qgd2l0aENyZWRlbnRpYWxzID0gISEodHlwZW9mIG92ZXJyaWRlcy53aXRoQ3JlZGVudGlhbHMgPiBcInVcIiA/IGNvbmZpZy53aXRoQ3JlZGVudGlhbHMgOiBvdmVycmlkZXMud2l0aENyZWRlbnRpYWxzKSwgdGltZW91dCA9IHR5cGVvZiBvdmVycmlkZXMudGltZW91dCA+IFwidVwiID8gY29uZmlnLnRpbWVvdXQgOiBvdmVycmlkZXMudGltZW91dDtcbiAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIG92ZXJyaWRlcywge1xuICAgIGhlYWRlcnM6IE9iamVjdC5hc3NpZ24oe30sIGhlYWRlcnMyLCBvdmVycmlkZXMuaGVhZGVycyB8fCB7fSksXG4gICAgdGltZW91dDogdHlwZW9mIHRpbWVvdXQgPiBcInVcIiA/IDUgKiA2MCAqIDFlMyA6IHRpbWVvdXQsXG4gICAgcHJveHk6IG92ZXJyaWRlcy5wcm94eSB8fCBjb25maWcucHJveHksXG4gICAganNvbjogITAsXG4gICAgd2l0aENyZWRlbnRpYWxzLFxuICAgIGZldGNoOiB0eXBlb2Ygb3ZlcnJpZGVzLmZldGNoID09IFwib2JqZWN0XCIgJiYgdHlwZW9mIGNvbmZpZy5mZXRjaCA9PSBcIm9iamVjdFwiID8geyAuLi5jb25maWcuZmV0Y2gsIC4uLm92ZXJyaWRlcy5mZXRjaCB9IDogb3ZlcnJpZGVzLmZldGNoIHx8IGNvbmZpZy5mZXRjaFxuICB9KTtcbn1cbmNvbnN0IGVuY29kZVF1ZXJ5U3RyaW5nID0gKHtcbiAgcXVlcnksXG4gIHBhcmFtcyA9IHt9LFxuICBvcHRpb25zID0ge31cbn0pID0+IHtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpLCB7IHRhZywgaW5jbHVkZU11dGF0aW9ucywgcmV0dXJuUXVlcnksIC4uLm9wdHMgfSA9IG9wdGlvbnM7XG4gIHRhZyAmJiBzZWFyY2hQYXJhbXMuYXBwZW5kKFwidGFnXCIsIHRhZyksIHNlYXJjaFBhcmFtcy5hcHBlbmQoXCJxdWVyeVwiLCBxdWVyeSk7XG4gIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKHBhcmFtcykpXG4gICAgc2VhcmNoUGFyYW1zLmFwcGVuZChgJCR7a2V5fWAsIEpTT04uc3RyaW5naWZ5KHZhbHVlKSk7XG4gIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKG9wdHMpKVxuICAgIHZhbHVlICYmIHNlYXJjaFBhcmFtcy5hcHBlbmQoa2V5LCBgJHt2YWx1ZX1gKTtcbiAgcmV0dXJuIHJldHVyblF1ZXJ5ID09PSAhMSAmJiBzZWFyY2hQYXJhbXMuYXBwZW5kKFwicmV0dXJuUXVlcnlcIiwgXCJmYWxzZVwiKSwgaW5jbHVkZU11dGF0aW9ucyA9PT0gITEgJiYgc2VhcmNoUGFyYW1zLmFwcGVuZChcImluY2x1ZGVNdXRhdGlvbnNcIiwgXCJmYWxzZVwiKSwgYD8ke3NlYXJjaFBhcmFtc31gO1xufSwgZXhjbHVkZUZhbHNleSA9IChwYXJhbSwgZGVmVmFsdWUpID0+IHBhcmFtID09PSAhMSA/IHZvaWQgMCA6IHR5cGVvZiBwYXJhbSA+IFwidVwiID8gZGVmVmFsdWUgOiBwYXJhbSwgZ2V0TXV0YXRpb25RdWVyeSA9IChvcHRpb25zID0ge30pID0+ICh7XG4gIGRyeVJ1bjogb3B0aW9ucy5kcnlSdW4sXG4gIHJldHVybklkczogITAsXG4gIHJldHVybkRvY3VtZW50czogZXhjbHVkZUZhbHNleShvcHRpb25zLnJldHVybkRvY3VtZW50cywgITApLFxuICB2aXNpYmlsaXR5OiBvcHRpb25zLnZpc2liaWxpdHkgfHwgXCJzeW5jXCIsXG4gIGF1dG9HZW5lcmF0ZUFycmF5S2V5czogb3B0aW9ucy5hdXRvR2VuZXJhdGVBcnJheUtleXMsXG4gIHNraXBDcm9zc0RhdGFzZXRSZWZlcmVuY2VWYWxpZGF0aW9uOiBvcHRpb25zLnNraXBDcm9zc0RhdGFzZXRSZWZlcmVuY2VWYWxpZGF0aW9uXG59KSwgaXNSZXNwb25zZSA9IChldmVudCkgPT4gZXZlbnQudHlwZSA9PT0gXCJyZXNwb25zZVwiLCBnZXRCb2R5ID0gKGV2ZW50KSA9PiBldmVudC5ib2R5LCBpbmRleEJ5ID0gKGRvY3MsIGF0dHIpID0+IGRvY3MucmVkdWNlKChpbmRleGVkLCBkb2MpID0+IChpbmRleGVkW2F0dHIoZG9jKV0gPSBkb2MsIGluZGV4ZWQpLCAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKSksIGdldFF1ZXJ5U2l6ZUxpbWl0ID0gMTEyNjQ7XG5mdW5jdGlvbiBfZmV0Y2goY2xpZW50LCBodHRwUmVxdWVzdCwgX3N0ZWdhLCBxdWVyeSwgX3BhcmFtcyA9IHt9LCBvcHRpb25zID0ge30pIHtcbiAgY29uc3Qgc3RlZ2EgPSBcInN0ZWdhXCIgaW4gb3B0aW9ucyA/IHtcbiAgICAuLi5fc3RlZ2EgfHwge30sXG4gICAgLi4udHlwZW9mIG9wdGlvbnMuc3RlZ2EgPT0gXCJib29sZWFuXCIgPyB7IGVuYWJsZWQ6IG9wdGlvbnMuc3RlZ2EgfSA6IG9wdGlvbnMuc3RlZ2EgfHwge31cbiAgfSA6IF9zdGVnYSwgcGFyYW1zID0gc3RlZ2EuZW5hYmxlZCA/IHN0ZWdhQ2xlYW4oX3BhcmFtcykgOiBfcGFyYW1zLCBtYXBSZXNwb25zZSA9IG9wdGlvbnMuZmlsdGVyUmVzcG9uc2UgPT09ICExID8gKHJlcykgPT4gcmVzIDogKHJlcykgPT4gcmVzLnJlc3VsdCwgeyBjYWNoZSwgbmV4dCwgLi4ub3B0cyB9ID0ge1xuICAgIC8vIE9wdCBvdXQgb2Ygc2V0dGluZyBhIGBzaWduYWxgIG9uIGFuIGludGVybmFsIGBmZXRjaGAgaWYgb25lIGlzbid0IHByb3ZpZGVkLlxuICAgIC8vIFRoaXMgaXMgbmVjZXNzYXJ5IGluIFJlYWN0IFNlcnZlciBDb21wb25lbnRzIHRvIGF2b2lkIG9wdGluZyBvdXQgb2YgUmVxdWVzdCBNZW1vaXphdGlvbi5cbiAgICB1c2VBYm9ydFNpZ25hbDogdHlwZW9mIG9wdGlvbnMuc2lnbmFsIDwgXCJ1XCIsXG4gICAgLy8gU2V0IGByZXN1bHRTb3VyY2VNYXAnIHdoZW4gc3RlZ2EgaXMgZW5hYmxlZCwgYXMgaXQncyByZXF1aXJlZCBmb3IgZW5jb2RpbmcuXG4gICAgcmVzdWx0U291cmNlTWFwOiBzdGVnYS5lbmFibGVkID8gXCJ3aXRoS2V5QXJyYXlTZWxlY3RvclwiIDogb3B0aW9ucy5yZXN1bHRTb3VyY2VNYXAsXG4gICAgLi4ub3B0aW9ucyxcbiAgICAvLyBEZWZhdWx0IHRvIG5vdCByZXR1cm5pbmcgdGhlIHF1ZXJ5LCB1bmxlc3MgYGZpbHRlclJlc3BvbnNlYCBpcyBgZmFsc2VgLFxuICAgIC8vIG9yIGByZXR1cm5RdWVyeWAgaXMgZXhwbGljaXRseSBzZXQuIGB0cnVlYCBpcyB0aGUgZGVmYXVsdCBpbiBDb250ZW50IExha2UsIHNvIHNraXAgaWYgdHJ1dGh5XG4gICAgcmV0dXJuUXVlcnk6IG9wdGlvbnMuZmlsdGVyUmVzcG9uc2UgPT09ICExICYmIG9wdGlvbnMucmV0dXJuUXVlcnkgIT09ICExXG4gIH0sIHJlcU9wdHMgPSB0eXBlb2YgY2FjaGUgPCBcInVcIiB8fCB0eXBlb2YgbmV4dCA8IFwidVwiID8geyAuLi5vcHRzLCBmZXRjaDogeyBjYWNoZSwgbmV4dCB9IH0gOiBvcHRzLCAkcmVxdWVzdCA9IF9kYXRhUmVxdWVzdChjbGllbnQsIGh0dHBSZXF1ZXN0LCBcInF1ZXJ5XCIsIHsgcXVlcnksIHBhcmFtcyB9LCByZXFPcHRzKTtcbiAgcmV0dXJuIHN0ZWdhLmVuYWJsZWQgPyAkcmVxdWVzdC5waXBlKFxuICAgIGNvbWJpbmVMYXRlc3RXaXRoKFxuICAgICAgZnJvbShcbiAgICAgICAgaW1wb3J0KFwiLi9fY2h1bmtzLWVzL3N0ZWdhRW5jb2RlU291cmNlTWFwLmpzXCIpLnRoZW4oZnVuY3Rpb24obikge1xuICAgICAgICAgIHJldHVybiBuLnN0ZWdhRW5jb2RlU291cmNlTWFwJDE7XG4gICAgICAgIH0pLnRoZW4oXG4gICAgICAgICAgKHsgc3RlZ2FFbmNvZGVTb3VyY2VNYXAgfSkgPT4gc3RlZ2FFbmNvZGVTb3VyY2VNYXBcbiAgICAgICAgKVxuICAgICAgKVxuICAgICksXG4gICAgbWFwKFxuICAgICAgKFtyZXMsIHN0ZWdhRW5jb2RlU291cmNlTWFwXSkgPT4ge1xuICAgICAgICBjb25zdCByZXN1bHQgPSBzdGVnYUVuY29kZVNvdXJjZU1hcChyZXMucmVzdWx0LCByZXMucmVzdWx0U291cmNlTWFwLCBzdGVnYSk7XG4gICAgICAgIHJldHVybiBtYXBSZXNwb25zZSh7IC4uLnJlcywgcmVzdWx0IH0pO1xuICAgICAgfVxuICAgIClcbiAgKSA6ICRyZXF1ZXN0LnBpcGUobWFwKG1hcFJlc3BvbnNlKSk7XG59XG5mdW5jdGlvbiBfZ2V0RG9jdW1lbnQoY2xpZW50LCBodHRwUmVxdWVzdCwgaWQsIG9wdHMgPSB7fSkge1xuICBjb25zdCBvcHRpb25zID0ge1xuICAgIHVyaTogX2dldERhdGFVcmwoY2xpZW50LCBcImRvY1wiLCBpZCksXG4gICAganNvbjogITAsXG4gICAgdGFnOiBvcHRzLnRhZyxcbiAgICBzaWduYWw6IG9wdHMuc2lnbmFsXG4gIH07XG4gIHJldHVybiBfcmVxdWVzdE9ic2VydmFibGUoY2xpZW50LCBodHRwUmVxdWVzdCwgb3B0aW9ucykucGlwZShcbiAgICBmaWx0ZXIoaXNSZXNwb25zZSksXG4gICAgbWFwKChldmVudCkgPT4gZXZlbnQuYm9keS5kb2N1bWVudHMgJiYgZXZlbnQuYm9keS5kb2N1bWVudHNbMF0pXG4gICk7XG59XG5mdW5jdGlvbiBfZ2V0RG9jdW1lbnRzKGNsaWVudCwgaHR0cFJlcXVlc3QsIGlkcywgb3B0cyA9IHt9KSB7XG4gIGNvbnN0IG9wdGlvbnMgPSB7XG4gICAgdXJpOiBfZ2V0RGF0YVVybChjbGllbnQsIFwiZG9jXCIsIGlkcy5qb2luKFwiLFwiKSksXG4gICAganNvbjogITAsXG4gICAgdGFnOiBvcHRzLnRhZyxcbiAgICBzaWduYWw6IG9wdHMuc2lnbmFsXG4gIH07XG4gIHJldHVybiBfcmVxdWVzdE9ic2VydmFibGUoY2xpZW50LCBodHRwUmVxdWVzdCwgb3B0aW9ucykucGlwZShcbiAgICBmaWx0ZXIoaXNSZXNwb25zZSksXG4gICAgbWFwKChldmVudCkgPT4ge1xuICAgICAgY29uc3QgaW5kZXhlZCA9IGluZGV4QnkoZXZlbnQuYm9keS5kb2N1bWVudHMgfHwgW10sIChkb2MpID0+IGRvYy5faWQpO1xuICAgICAgcmV0dXJuIGlkcy5tYXAoKGlkKSA9PiBpbmRleGVkW2lkXSB8fCBudWxsKTtcbiAgICB9KVxuICApO1xufVxuZnVuY3Rpb24gX2NyZWF0ZUlmTm90RXhpc3RzKGNsaWVudCwgaHR0cFJlcXVlc3QsIGRvYywgb3B0aW9ucykge1xuICByZXR1cm4gcmVxdWlyZURvY3VtZW50SWQoXCJjcmVhdGVJZk5vdEV4aXN0c1wiLCBkb2MpLCBfY3JlYXRlKGNsaWVudCwgaHR0cFJlcXVlc3QsIGRvYywgXCJjcmVhdGVJZk5vdEV4aXN0c1wiLCBvcHRpb25zKTtcbn1cbmZ1bmN0aW9uIF9jcmVhdGVPclJlcGxhY2UoY2xpZW50LCBodHRwUmVxdWVzdCwgZG9jLCBvcHRpb25zKSB7XG4gIHJldHVybiByZXF1aXJlRG9jdW1lbnRJZChcImNyZWF0ZU9yUmVwbGFjZVwiLCBkb2MpLCBfY3JlYXRlKGNsaWVudCwgaHR0cFJlcXVlc3QsIGRvYywgXCJjcmVhdGVPclJlcGxhY2VcIiwgb3B0aW9ucyk7XG59XG5mdW5jdGlvbiBfZGVsZXRlKGNsaWVudCwgaHR0cFJlcXVlc3QsIHNlbGVjdGlvbiwgb3B0aW9ucykge1xuICByZXR1cm4gX2RhdGFSZXF1ZXN0KFxuICAgIGNsaWVudCxcbiAgICBodHRwUmVxdWVzdCxcbiAgICBcIm11dGF0ZVwiLFxuICAgIHsgbXV0YXRpb25zOiBbeyBkZWxldGU6IGdldFNlbGVjdGlvbihzZWxlY3Rpb24pIH1dIH0sXG4gICAgb3B0aW9uc1xuICApO1xufVxuZnVuY3Rpb24gX211dGF0ZShjbGllbnQsIGh0dHBSZXF1ZXN0LCBtdXRhdGlvbnMsIG9wdGlvbnMpIHtcbiAgbGV0IG11dDtcbiAgbXV0YXRpb25zIGluc3RhbmNlb2YgUGF0Y2ggfHwgbXV0YXRpb25zIGluc3RhbmNlb2YgT2JzZXJ2YWJsZVBhdGNoID8gbXV0ID0geyBwYXRjaDogbXV0YXRpb25zLnNlcmlhbGl6ZSgpIH0gOiBtdXRhdGlvbnMgaW5zdGFuY2VvZiBUcmFuc2FjdGlvbiB8fCBtdXRhdGlvbnMgaW5zdGFuY2VvZiBPYnNlcnZhYmxlVHJhbnNhY3Rpb24gPyBtdXQgPSBtdXRhdGlvbnMuc2VyaWFsaXplKCkgOiBtdXQgPSBtdXRhdGlvbnM7XG4gIGNvbnN0IG11dHMgPSBBcnJheS5pc0FycmF5KG11dCkgPyBtdXQgOiBbbXV0XSwgdHJhbnNhY3Rpb25JZCA9IG9wdGlvbnMgJiYgb3B0aW9ucy50cmFuc2FjdGlvbklkIHx8IHZvaWQgMDtcbiAgcmV0dXJuIF9kYXRhUmVxdWVzdChjbGllbnQsIGh0dHBSZXF1ZXN0LCBcIm11dGF0ZVwiLCB7IG11dGF0aW9uczogbXV0cywgdHJhbnNhY3Rpb25JZCB9LCBvcHRpb25zKTtcbn1cbmZ1bmN0aW9uIF9hY3Rpb24oY2xpZW50LCBodHRwUmVxdWVzdCwgYWN0aW9ucywgb3B0aW9ucykge1xuICBjb25zdCBhY3RzID0gQXJyYXkuaXNBcnJheShhY3Rpb25zKSA/IGFjdGlvbnMgOiBbYWN0aW9uc10sIHRyYW5zYWN0aW9uSWQgPSBvcHRpb25zICYmIG9wdGlvbnMudHJhbnNhY3Rpb25JZCB8fCB2b2lkIDAsIHNraXBDcm9zc0RhdGFzZXRSZWZlcmVuY2VWYWxpZGF0aW9uID0gb3B0aW9ucyAmJiBvcHRpb25zLnNraXBDcm9zc0RhdGFzZXRSZWZlcmVuY2VWYWxpZGF0aW9uIHx8IHZvaWQgMCwgZHJ5UnVuID0gb3B0aW9ucyAmJiBvcHRpb25zLmRyeVJ1biB8fCB2b2lkIDA7XG4gIHJldHVybiBfZGF0YVJlcXVlc3QoXG4gICAgY2xpZW50LFxuICAgIGh0dHBSZXF1ZXN0LFxuICAgIFwiYWN0aW9uc1wiLFxuICAgIHsgYWN0aW9uczogYWN0cywgdHJhbnNhY3Rpb25JZCwgc2tpcENyb3NzRGF0YXNldFJlZmVyZW5jZVZhbGlkYXRpb24sIGRyeVJ1biB9LFxuICAgIG9wdGlvbnNcbiAgKTtcbn1cbmZ1bmN0aW9uIF9kYXRhUmVxdWVzdChjbGllbnQsIGh0dHBSZXF1ZXN0LCBlbmRwb2ludCwgYm9keSwgb3B0aW9ucyA9IHt9KSB7XG4gIGNvbnN0IGlzTXV0YXRpb24gPSBlbmRwb2ludCA9PT0gXCJtdXRhdGVcIiwgaXNBY3Rpb24gPSBlbmRwb2ludCA9PT0gXCJhY3Rpb25zXCIsIGlzUXVlcnkyID0gZW5kcG9pbnQgPT09IFwicXVlcnlcIiwgc3RyUXVlcnkgPSBpc011dGF0aW9uIHx8IGlzQWN0aW9uID8gXCJcIiA6IGVuY29kZVF1ZXJ5U3RyaW5nKGJvZHkpLCB1c2VHZXQgPSAhaXNNdXRhdGlvbiAmJiAhaXNBY3Rpb24gJiYgc3RyUXVlcnkubGVuZ3RoIDwgZ2V0UXVlcnlTaXplTGltaXQsIHN0cmluZ1F1ZXJ5ID0gdXNlR2V0ID8gc3RyUXVlcnkgOiBcIlwiLCByZXR1cm5GaXJzdCA9IG9wdGlvbnMucmV0dXJuRmlyc3QsIHsgdGltZW91dCwgdG9rZW4sIHRhZywgaGVhZGVyczogaGVhZGVyczIsIHJldHVyblF1ZXJ5LCBsYXN0TGl2ZUV2ZW50SWQsIGNhY2hlTW9kZSB9ID0gb3B0aW9ucywgdXJpID0gX2dldERhdGFVcmwoY2xpZW50LCBlbmRwb2ludCwgc3RyaW5nUXVlcnkpLCByZXFPcHRpb25zID0ge1xuICAgIG1ldGhvZDogdXNlR2V0ID8gXCJHRVRcIiA6IFwiUE9TVFwiLFxuICAgIHVyaSxcbiAgICBqc29uOiAhMCxcbiAgICBib2R5OiB1c2VHZXQgPyB2b2lkIDAgOiBib2R5LFxuICAgIHF1ZXJ5OiBpc011dGF0aW9uICYmIGdldE11dGF0aW9uUXVlcnkob3B0aW9ucyksXG4gICAgdGltZW91dCxcbiAgICBoZWFkZXJzOiBoZWFkZXJzMixcbiAgICB0b2tlbixcbiAgICB0YWcsXG4gICAgcmV0dXJuUXVlcnksXG4gICAgcGVyc3BlY3RpdmU6IG9wdGlvbnMucGVyc3BlY3RpdmUsXG4gICAgcmVzdWx0U291cmNlTWFwOiBvcHRpb25zLnJlc3VsdFNvdXJjZU1hcCxcbiAgICBsYXN0TGl2ZUV2ZW50SWQ6IEFycmF5LmlzQXJyYXkobGFzdExpdmVFdmVudElkKSA/IGxhc3RMaXZlRXZlbnRJZFswXSA6IGxhc3RMaXZlRXZlbnRJZCxcbiAgICBjYWNoZU1vZGUsXG4gICAgY2FuVXNlQ2RuOiBpc1F1ZXJ5MixcbiAgICBzaWduYWw6IG9wdGlvbnMuc2lnbmFsLFxuICAgIGZldGNoOiBvcHRpb25zLmZldGNoLFxuICAgIHVzZUFib3J0U2lnbmFsOiBvcHRpb25zLnVzZUFib3J0U2lnbmFsLFxuICAgIHVzZUNkbjogb3B0aW9ucy51c2VDZG5cbiAgfTtcbiAgcmV0dXJuIF9yZXF1ZXN0T2JzZXJ2YWJsZShjbGllbnQsIGh0dHBSZXF1ZXN0LCByZXFPcHRpb25zKS5waXBlKFxuICAgIGZpbHRlcihpc1Jlc3BvbnNlKSxcbiAgICBtYXAoZ2V0Qm9keSksXG4gICAgbWFwKChyZXMpID0+IHtcbiAgICAgIGlmICghaXNNdXRhdGlvbilcbiAgICAgICAgcmV0dXJuIHJlcztcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSByZXMucmVzdWx0cyB8fCBbXTtcbiAgICAgIGlmIChvcHRpb25zLnJldHVybkRvY3VtZW50cylcbiAgICAgICAgcmV0dXJuIHJldHVybkZpcnN0ID8gcmVzdWx0c1swXSAmJiByZXN1bHRzWzBdLmRvY3VtZW50IDogcmVzdWx0cy5tYXAoKG11dCkgPT4gbXV0LmRvY3VtZW50KTtcbiAgICAgIGNvbnN0IGtleSA9IHJldHVybkZpcnN0ID8gXCJkb2N1bWVudElkXCIgOiBcImRvY3VtZW50SWRzXCIsIGlkcyA9IHJldHVybkZpcnN0ID8gcmVzdWx0c1swXSAmJiByZXN1bHRzWzBdLmlkIDogcmVzdWx0cy5tYXAoKG11dCkgPT4gbXV0LmlkKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHRyYW5zYWN0aW9uSWQ6IHJlcy50cmFuc2FjdGlvbklkLFxuICAgICAgICByZXN1bHRzLFxuICAgICAgICBba2V5XTogaWRzXG4gICAgICB9O1xuICAgIH0pXG4gICk7XG59XG5mdW5jdGlvbiBfY3JlYXRlKGNsaWVudCwgaHR0cFJlcXVlc3QsIGRvYywgb3AsIG9wdGlvbnMgPSB7fSkge1xuICBjb25zdCBtdXRhdGlvbiA9IHsgW29wXTogZG9jIH0sIG9wdHMgPSBPYmplY3QuYXNzaWduKHsgcmV0dXJuRmlyc3Q6ICEwLCByZXR1cm5Eb2N1bWVudHM6ICEwIH0sIG9wdGlvbnMpO1xuICByZXR1cm4gX2RhdGFSZXF1ZXN0KGNsaWVudCwgaHR0cFJlcXVlc3QsIFwibXV0YXRlXCIsIHsgbXV0YXRpb25zOiBbbXV0YXRpb25dIH0sIG9wdHMpO1xufVxuY29uc3QgaGFzRGF0YUNvbmZpZyA9IChjbGllbnQpID0+IGNsaWVudC5jb25maWcoKS5kYXRhc2V0ICE9PSB2b2lkIDAgJiYgY2xpZW50LmNvbmZpZygpLnByb2plY3RJZCAhPT0gdm9pZCAwIHx8IGNsaWVudC5jb25maWcoKVtcIn5leHBlcmltZW50YWxfcmVzb3VyY2VcIl0gIT09IHZvaWQgMCwgaXNRdWVyeSA9IChjbGllbnQsIHVyaSkgPT4gaGFzRGF0YUNvbmZpZyhjbGllbnQpICYmIHVyaS5zdGFydHNXaXRoKF9nZXREYXRhVXJsKGNsaWVudCwgXCJxdWVyeVwiKSksIGlzTXV0YXRlID0gKGNsaWVudCwgdXJpKSA9PiBoYXNEYXRhQ29uZmlnKGNsaWVudCkgJiYgdXJpLnN0YXJ0c1dpdGgoX2dldERhdGFVcmwoY2xpZW50LCBcIm11dGF0ZVwiKSksIGlzRG9jID0gKGNsaWVudCwgdXJpKSA9PiBoYXNEYXRhQ29uZmlnKGNsaWVudCkgJiYgdXJpLnN0YXJ0c1dpdGgoX2dldERhdGFVcmwoY2xpZW50LCBcImRvY1wiLCBcIlwiKSksIGlzTGlzdGVuZXIgPSAoY2xpZW50LCB1cmkpID0+IGhhc0RhdGFDb25maWcoY2xpZW50KSAmJiB1cmkuc3RhcnRzV2l0aChfZ2V0RGF0YVVybChjbGllbnQsIFwibGlzdGVuXCIpKSwgaXNIaXN0b3J5ID0gKGNsaWVudCwgdXJpKSA9PiBoYXNEYXRhQ29uZmlnKGNsaWVudCkgJiYgdXJpLnN0YXJ0c1dpdGgoX2dldERhdGFVcmwoY2xpZW50LCBcImhpc3RvcnlcIiwgXCJcIikpLCBpc0RhdGEgPSAoY2xpZW50LCB1cmkpID0+IHVyaS5zdGFydHNXaXRoKFwiL2RhdGEvXCIpIHx8IGlzUXVlcnkoY2xpZW50LCB1cmkpIHx8IGlzTXV0YXRlKGNsaWVudCwgdXJpKSB8fCBpc0RvYyhjbGllbnQsIHVyaSkgfHwgaXNMaXN0ZW5lcihjbGllbnQsIHVyaSkgfHwgaXNIaXN0b3J5KGNsaWVudCwgdXJpKTtcbmZ1bmN0aW9uIF9yZXF1ZXN0T2JzZXJ2YWJsZShjbGllbnQsIGh0dHBSZXF1ZXN0LCBvcHRpb25zKSB7XG4gIGNvbnN0IHVyaSA9IG9wdGlvbnMudXJsIHx8IG9wdGlvbnMudXJpLCBjb25maWcgPSBjbGllbnQuY29uZmlnKCksIGNhblVzZUNkbiA9IHR5cGVvZiBvcHRpb25zLmNhblVzZUNkbiA+IFwidVwiID8gW1wiR0VUXCIsIFwiSEVBRFwiXS5pbmRleE9mKG9wdGlvbnMubWV0aG9kIHx8IFwiR0VUXCIpID49IDAgJiYgaXNEYXRhKGNsaWVudCwgdXJpKSA6IG9wdGlvbnMuY2FuVXNlQ2RuO1xuICBsZXQgdXNlQ2RuID0gKG9wdGlvbnMudXNlQ2RuID8/IGNvbmZpZy51c2VDZG4pICYmIGNhblVzZUNkbjtcbiAgY29uc3QgdGFnID0gb3B0aW9ucy50YWcgJiYgY29uZmlnLnJlcXVlc3RUYWdQcmVmaXggPyBbY29uZmlnLnJlcXVlc3RUYWdQcmVmaXgsIG9wdGlvbnMudGFnXS5qb2luKFwiLlwiKSA6IG9wdGlvbnMudGFnIHx8IGNvbmZpZy5yZXF1ZXN0VGFnUHJlZml4O1xuICBpZiAodGFnICYmIG9wdGlvbnMudGFnICE9PSBudWxsICYmIChvcHRpb25zLnF1ZXJ5ID0geyB0YWc6IHJlcXVlc3RUYWcodGFnKSwgLi4ub3B0aW9ucy5xdWVyeSB9KSwgW1wiR0VUXCIsIFwiSEVBRFwiLCBcIlBPU1RcIl0uaW5kZXhPZihvcHRpb25zLm1ldGhvZCB8fCBcIkdFVFwiKSA+PSAwICYmIGlzUXVlcnkoY2xpZW50LCB1cmkpKSB7XG4gICAgY29uc3QgcmVzdWx0U291cmNlTWFwID0gb3B0aW9ucy5yZXN1bHRTb3VyY2VNYXAgPz8gY29uZmlnLnJlc3VsdFNvdXJjZU1hcDtcbiAgICByZXN1bHRTb3VyY2VNYXAgIT09IHZvaWQgMCAmJiByZXN1bHRTb3VyY2VNYXAgIT09ICExICYmIChvcHRpb25zLnF1ZXJ5ID0geyByZXN1bHRTb3VyY2VNYXAsIC4uLm9wdGlvbnMucXVlcnkgfSk7XG4gICAgY29uc3QgcGVyc3BlY3RpdmVPcHRpb24gPSBvcHRpb25zLnBlcnNwZWN0aXZlIHx8IGNvbmZpZy5wZXJzcGVjdGl2ZTtcbiAgICB0eXBlb2YgcGVyc3BlY3RpdmVPcHRpb24gPCBcInVcIiAmJiAocGVyc3BlY3RpdmVPcHRpb24gPT09IFwicHJldmlld0RyYWZ0c1wiICYmIHByaW50UHJldmlld0RyYWZ0c0RlcHJlY2F0aW9uV2FybmluZygpLCB2YWxpZGF0ZUFwaVBlcnNwZWN0aXZlKHBlcnNwZWN0aXZlT3B0aW9uKSwgb3B0aW9ucy5xdWVyeSA9IHtcbiAgICAgIHBlcnNwZWN0aXZlOiBBcnJheS5pc0FycmF5KHBlcnNwZWN0aXZlT3B0aW9uKSA/IHBlcnNwZWN0aXZlT3B0aW9uLmpvaW4oXCIsXCIpIDogcGVyc3BlY3RpdmVPcHRpb24sXG4gICAgICAuLi5vcHRpb25zLnF1ZXJ5XG4gICAgfSwgKEFycmF5LmlzQXJyYXkocGVyc3BlY3RpdmVPcHRpb24pICYmIHBlcnNwZWN0aXZlT3B0aW9uLmxlbmd0aCA+IDAgfHwgLy8gcHJldmlld0RyYWZ0cyB3YXMgcmVuYW1lZCB0byBkcmFmdHMsIGJ1dCBrZWVwIGZvciBiYWNrd2FyZHMgY29tcGF0XG4gICAgcGVyc3BlY3RpdmVPcHRpb24gPT09IFwicHJldmlld0RyYWZ0c1wiIHx8IHBlcnNwZWN0aXZlT3B0aW9uID09PSBcImRyYWZ0c1wiKSAmJiB1c2VDZG4gJiYgKHVzZUNkbiA9ICExLCBwcmludENkblByZXZpZXdEcmFmdHNXYXJuaW5nKCkpKSwgb3B0aW9ucy5sYXN0TGl2ZUV2ZW50SWQgJiYgKG9wdGlvbnMucXVlcnkgPSB7IC4uLm9wdGlvbnMucXVlcnksIGxhc3RMaXZlRXZlbnRJZDogb3B0aW9ucy5sYXN0TGl2ZUV2ZW50SWQgfSksIG9wdGlvbnMucmV0dXJuUXVlcnkgPT09ICExICYmIChvcHRpb25zLnF1ZXJ5ID0geyByZXR1cm5RdWVyeTogXCJmYWxzZVwiLCAuLi5vcHRpb25zLnF1ZXJ5IH0pLCB1c2VDZG4gJiYgb3B0aW9ucy5jYWNoZU1vZGUgPT0gXCJub1N0YWxlXCIgJiYgKG9wdGlvbnMucXVlcnkgPSB7IGNhY2hlTW9kZTogXCJub1N0YWxlXCIsIC4uLm9wdGlvbnMucXVlcnkgfSk7XG4gIH1cbiAgY29uc3QgcmVxT3B0aW9ucyA9IHJlcXVlc3RPcHRpb25zKFxuICAgIGNvbmZpZyxcbiAgICBPYmplY3QuYXNzaWduKHt9LCBvcHRpb25zLCB7XG4gICAgICB1cmw6IF9nZXRVcmwoY2xpZW50LCB1cmksIHVzZUNkbilcbiAgICB9KVxuICApLCByZXF1ZXN0ID0gbmV3IE9ic2VydmFibGUoXG4gICAgKHN1YnNjcmliZXIpID0+IGh0dHBSZXF1ZXN0KHJlcU9wdGlvbnMsIGNvbmZpZy5yZXF1ZXN0ZXIpLnN1YnNjcmliZShzdWJzY3JpYmVyKVxuICApO1xuICByZXR1cm4gb3B0aW9ucy5zaWduYWwgPyByZXF1ZXN0LnBpcGUoX3dpdGhBYm9ydFNpZ25hbChvcHRpb25zLnNpZ25hbCkpIDogcmVxdWVzdDtcbn1cbmZ1bmN0aW9uIF9yZXF1ZXN0KGNsaWVudCwgaHR0cFJlcXVlc3QsIG9wdGlvbnMpIHtcbiAgcmV0dXJuIF9yZXF1ZXN0T2JzZXJ2YWJsZShjbGllbnQsIGh0dHBSZXF1ZXN0LCBvcHRpb25zKS5waXBlKFxuICAgIGZpbHRlcigoZXZlbnQpID0+IGV2ZW50LnR5cGUgPT09IFwicmVzcG9uc2VcIiksXG4gICAgbWFwKChldmVudCkgPT4gZXZlbnQuYm9keSlcbiAgKTtcbn1cbmZ1bmN0aW9uIF9nZXREYXRhVXJsKGNsaWVudCwgb3BlcmF0aW9uLCBwYXRoKSB7XG4gIGNvbnN0IGNvbmZpZyA9IGNsaWVudC5jb25maWcoKTtcbiAgaWYgKGNvbmZpZ1tcIn5leHBlcmltZW50YWxfcmVzb3VyY2VcIl0pIHtcbiAgICByZXNvdXJjZUNvbmZpZyhjb25maWcpO1xuICAgIGNvbnN0IHJlc291cmNlQmFzZSA9IHJlc291cmNlRGF0YUJhc2UoY29uZmlnKSwgdXJpMiA9IHBhdGggIT09IHZvaWQgMCA/IGAke29wZXJhdGlvbn0vJHtwYXRofWAgOiBvcGVyYXRpb247XG4gICAgcmV0dXJuIGAke3Jlc291cmNlQmFzZX0vJHt1cmkyfWAucmVwbGFjZSgvXFwvKCR8XFw/KS8sIFwiJDFcIik7XG4gIH1cbiAgY29uc3QgY2F0YWxvZyA9IGhhc0RhdGFzZXQoY29uZmlnKSwgYmFzZVVyaSA9IGAvJHtvcGVyYXRpb259LyR7Y2F0YWxvZ31gO1xuICByZXR1cm4gYC9kYXRhJHtwYXRoICE9PSB2b2lkIDAgPyBgJHtiYXNlVXJpfS8ke3BhdGh9YCA6IGJhc2VVcml9YC5yZXBsYWNlKC9cXC8oJHxcXD8pLywgXCIkMVwiKTtcbn1cbmZ1bmN0aW9uIF9nZXRVcmwoY2xpZW50LCB1cmksIGNhblVzZUNkbiA9ICExKSB7XG4gIGNvbnN0IHsgdXJsLCBjZG5VcmwgfSA9IGNsaWVudC5jb25maWcoKTtcbiAgcmV0dXJuIGAke2NhblVzZUNkbiA/IGNkblVybCA6IHVybH0vJHt1cmkucmVwbGFjZSgvXlxcLy8sIFwiXCIpfWA7XG59XG5mdW5jdGlvbiBfd2l0aEFib3J0U2lnbmFsKHNpZ25hbCkge1xuICByZXR1cm4gKGlucHV0KSA9PiBuZXcgT2JzZXJ2YWJsZSgob2JzZXJ2ZXIpID0+IHtcbiAgICBjb25zdCBhYm9ydCA9ICgpID0+IG9ic2VydmVyLmVycm9yKF9jcmVhdGVBYm9ydEVycm9yKHNpZ25hbCkpO1xuICAgIGlmIChzaWduYWwgJiYgc2lnbmFsLmFib3J0ZWQpIHtcbiAgICAgIGFib3J0KCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IHN1YnNjcmlwdGlvbiA9IGlucHV0LnN1YnNjcmliZShvYnNlcnZlcik7XG4gICAgcmV0dXJuIHNpZ25hbC5hZGRFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgYWJvcnQpLCAoKSA9PiB7XG4gICAgICBzaWduYWwucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImFib3J0XCIsIGFib3J0KSwgc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gICAgfTtcbiAgfSk7XG59XG5jb25zdCBpc0RvbUV4Y2VwdGlvblN1cHBvcnRlZCA9ICEhZ2xvYmFsVGhpcy5ET01FeGNlcHRpb247XG5mdW5jdGlvbiBfY3JlYXRlQWJvcnRFcnJvcihzaWduYWwpIHtcbiAgaWYgKGlzRG9tRXhjZXB0aW9uU3VwcG9ydGVkKVxuICAgIHJldHVybiBuZXcgRE9NRXhjZXB0aW9uKHNpZ25hbD8ucmVhc29uID8/IFwiVGhlIG9wZXJhdGlvbiB3YXMgYWJvcnRlZC5cIiwgXCJBYm9ydEVycm9yXCIpO1xuICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcihzaWduYWw/LnJlYXNvbiA/PyBcIlRoZSBvcGVyYXRpb24gd2FzIGFib3J0ZWQuXCIpO1xuICByZXR1cm4gZXJyb3IubmFtZSA9IFwiQWJvcnRFcnJvclwiLCBlcnJvcjtcbn1cbmNvbnN0IHJlc291cmNlRGF0YUJhc2UgPSAoY29uZmlnKSA9PiB7XG4gIGlmICghY29uZmlnW1wifmV4cGVyaW1lbnRhbF9yZXNvdXJjZVwiXSlcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJgcmVzb3VyY2VgIG11c3QgYmUgcHJvdmlkZWQgdG8gcGVyZm9ybSByZXNvdXJjZSBxdWVyaWVzXCIpO1xuICBjb25zdCB7IHR5cGUsIGlkIH0gPSBjb25maWdbXCJ+ZXhwZXJpbWVudGFsX3Jlc291cmNlXCJdO1xuICBzd2l0Y2ggKHR5cGUpIHtcbiAgICBjYXNlIFwiZGF0YXNldFwiOiB7XG4gICAgICBjb25zdCBzZWdtZW50cyA9IGlkLnNwbGl0KFwiLlwiKTtcbiAgICAgIGlmIChzZWdtZW50cy5sZW5ndGggIT09IDIpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRGF0YXNldCBJRCBtdXN0IGJlIGluIHRoZSBmb3JtYXQgXCJwcm9qZWN0LmRhdGFzZXRcIicpO1xuICAgICAgcmV0dXJuIGAvcHJvamVjdHMvJHtzZWdtZW50c1swXX0vZGF0YXNldHMvJHtzZWdtZW50c1sxXX1gO1xuICAgIH1cbiAgICBjYXNlIFwiY2FudmFzXCI6XG4gICAgICByZXR1cm4gYC9jYW52YXNlcy8ke2lkfWA7XG4gICAgY2FzZSBcIm1lZGlhLWxpYnJhcnlcIjpcbiAgICAgIHJldHVybiBgL21lZGlhLWxpYnJhcmllcy8ke2lkfWA7XG4gICAgY2FzZSBcImRhc2hib2FyZFwiOlxuICAgICAgcmV0dXJuIGAvZGFzaGJvYXJkcy8ke2lkfWA7XG4gICAgZGVmYXVsdDpcbiAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgcmVzb3VyY2UgdHlwZTogJHt0eXBlLnRvU3RyaW5nKCl9YCk7XG4gIH1cbn07XG5mdW5jdGlvbiBfZ2VuZXJhdGUoY2xpZW50LCBodHRwUmVxdWVzdCwgcmVxdWVzdCkge1xuICBjb25zdCBkYXRhc2V0MiA9IGhhc0RhdGFzZXQoY2xpZW50LmNvbmZpZygpKTtcbiAgcmV0dXJuIF9yZXF1ZXN0KGNsaWVudCwgaHR0cFJlcXVlc3QsIHtcbiAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgIHVyaTogYC9hZ2VudC9hY3Rpb24vZ2VuZXJhdGUvJHtkYXRhc2V0Mn1gLFxuICAgIGJvZHk6IHJlcXVlc3RcbiAgfSk7XG59XG5mdW5jdGlvbiBfdHJhbnNmb3JtKGNsaWVudCwgaHR0cFJlcXVlc3QsIHJlcXVlc3QpIHtcbiAgY29uc3QgZGF0YXNldDIgPSBoYXNEYXRhc2V0KGNsaWVudC5jb25maWcoKSk7XG4gIHJldHVybiBfcmVxdWVzdChjbGllbnQsIGh0dHBSZXF1ZXN0LCB7XG4gICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICB1cmk6IGAvYWdlbnQvYWN0aW9uL3RyYW5zZm9ybS8ke2RhdGFzZXQyfWAsXG4gICAgYm9keTogcmVxdWVzdFxuICB9KTtcbn1cbmZ1bmN0aW9uIF90cmFuc2xhdGUoY2xpZW50LCBodHRwUmVxdWVzdCwgcmVxdWVzdCkge1xuICBjb25zdCBkYXRhc2V0MiA9IGhhc0RhdGFzZXQoY2xpZW50LmNvbmZpZygpKTtcbiAgcmV0dXJuIF9yZXF1ZXN0KGNsaWVudCwgaHR0cFJlcXVlc3QsIHtcbiAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgIHVyaTogYC9hZ2VudC9hY3Rpb24vdHJhbnNsYXRlLyR7ZGF0YXNldDJ9YCxcbiAgICBib2R5OiByZXF1ZXN0XG4gIH0pO1xufVxuY2xhc3MgT2JzZXJ2YWJsZUFnZW50c0FjdGlvbkNsaWVudCB7XG4gICNjbGllbnQ7XG4gICNodHRwUmVxdWVzdDtcbiAgY29uc3RydWN0b3IoY2xpZW50LCBodHRwUmVxdWVzdCkge1xuICAgIHRoaXMuI2NsaWVudCA9IGNsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QgPSBodHRwUmVxdWVzdDtcbiAgfVxuICAvKipcbiAgICogUnVuIGFuIGluc3RydWN0aW9uIHRvIGdlbmVyYXRlIGNvbnRlbnQgaW4gYSB0YXJnZXQgZG9jdW1lbnQuXG4gICAqIEBwYXJhbSByZXF1ZXN0IC0gaW5zdHJ1Y3Rpb24gcmVxdWVzdFxuICAgKi9cbiAgZ2VuZXJhdGUocmVxdWVzdCkge1xuICAgIHJldHVybiBfZ2VuZXJhdGUodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwgcmVxdWVzdCk7XG4gIH1cbiAgLyoqXG4gICAqIFRyYW5zZm9ybSBhIHRhcmdldCBkb2N1bWVudCBiYXNlZCBvbiBhIHNvdXJjZS5cbiAgICogQHBhcmFtIHJlcXVlc3QgLSB0cmFuc2xhdGlvbiByZXF1ZXN0XG4gICAqL1xuICB0cmFuc2Zvcm0ocmVxdWVzdCkge1xuICAgIHJldHVybiBfdHJhbnNmb3JtKHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIHJlcXVlc3QpO1xuICB9XG4gIC8qKlxuICAgKiBUcmFuc2xhdGUgYSB0YXJnZXQgZG9jdW1lbnQgYmFzZWQgb24gYSBzb3VyY2UuXG4gICAqIEBwYXJhbSByZXF1ZXN0IC0gdHJhbnNsYXRpb24gcmVxdWVzdFxuICAgKi9cbiAgdHJhbnNsYXRlKHJlcXVlc3QpIHtcbiAgICByZXR1cm4gX3RyYW5zbGF0ZSh0aGlzLiNjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0LCByZXF1ZXN0KTtcbiAgfVxufVxuY2xhc3MgQWdlbnRBY3Rpb25zQ2xpZW50IHtcbiAgI2NsaWVudDtcbiAgI2h0dHBSZXF1ZXN0O1xuICBjb25zdHJ1Y3RvcihjbGllbnQsIGh0dHBSZXF1ZXN0KSB7XG4gICAgdGhpcy4jY2xpZW50ID0gY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCA9IGh0dHBSZXF1ZXN0O1xuICB9XG4gIC8qKlxuICAgKiBSdW4gYW4gaW5zdHJ1Y3Rpb24gdG8gZ2VuZXJhdGUgY29udGVudCBpbiBhIHRhcmdldCBkb2N1bWVudC5cbiAgICogQHBhcmFtIHJlcXVlc3QgLSBpbnN0cnVjdGlvbiByZXF1ZXN0XG4gICAqL1xuICBnZW5lcmF0ZShyZXF1ZXN0KSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oX2dlbmVyYXRlKHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIHJlcXVlc3QpKTtcbiAgfVxuICAvKipcbiAgICogVHJhbnNmb3JtIGEgdGFyZ2V0IGRvY3VtZW50IGJhc2VkIG9uIGEgc291cmNlLlxuICAgKiBAcGFyYW0gcmVxdWVzdCAtIHRyYW5zbGF0aW9uIHJlcXVlc3RcbiAgICovXG4gIHRyYW5zZm9ybShyZXF1ZXN0KSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oX3RyYW5zZm9ybSh0aGlzLiNjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0LCByZXF1ZXN0KSk7XG4gIH1cbiAgLyoqXG4gICAqIFRyYW5zbGF0ZSBhIHRhcmdldCBkb2N1bWVudCBiYXNlZCBvbiBhIHNvdXJjZS5cbiAgICogQHBhcmFtIHJlcXVlc3QgLSB0cmFuc2xhdGlvbiByZXF1ZXN0XG4gICAqL1xuICB0cmFuc2xhdGUocmVxdWVzdCkge1xuICAgIHJldHVybiBsYXN0VmFsdWVGcm9tKF90cmFuc2xhdGUodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwgcmVxdWVzdCkpO1xuICB9XG59XG5jbGFzcyBPYnNlcnZhYmxlQXNzZXRzQ2xpZW50IHtcbiAgI2NsaWVudDtcbiAgI2h0dHBSZXF1ZXN0O1xuICBjb25zdHJ1Y3RvcihjbGllbnQsIGh0dHBSZXF1ZXN0KSB7XG4gICAgdGhpcy4jY2xpZW50ID0gY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCA9IGh0dHBSZXF1ZXN0O1xuICB9XG4gIHVwbG9hZChhc3NldFR5cGUsIGJvZHksIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gX3VwbG9hZCh0aGlzLiNjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0LCBhc3NldFR5cGUsIGJvZHksIG9wdGlvbnMpO1xuICB9XG59XG5jbGFzcyBBc3NldHNDbGllbnQge1xuICAjY2xpZW50O1xuICAjaHR0cFJlcXVlc3Q7XG4gIGNvbnN0cnVjdG9yKGNsaWVudCwgaHR0cFJlcXVlc3QpIHtcbiAgICB0aGlzLiNjbGllbnQgPSBjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0ID0gaHR0cFJlcXVlc3Q7XG4gIH1cbiAgdXBsb2FkKGFzc2V0VHlwZSwgYm9keSwgb3B0aW9ucykge1xuICAgIGNvbnN0IG9ic2VydmFibGUyID0gX3VwbG9hZCh0aGlzLiNjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0LCBhc3NldFR5cGUsIGJvZHksIG9wdGlvbnMpO1xuICAgIHJldHVybiBsYXN0VmFsdWVGcm9tKFxuICAgICAgb2JzZXJ2YWJsZTIucGlwZShcbiAgICAgICAgZmlsdGVyKChldmVudCkgPT4gZXZlbnQudHlwZSA9PT0gXCJyZXNwb25zZVwiKSxcbiAgICAgICAgbWFwKFxuICAgICAgICAgIChldmVudCkgPT4gZXZlbnQuYm9keS5kb2N1bWVudFxuICAgICAgICApXG4gICAgICApXG4gICAgKTtcbiAgfVxufVxuZnVuY3Rpb24gX3VwbG9hZChjbGllbnQsIGh0dHBSZXF1ZXN0LCBhc3NldFR5cGUsIGJvZHksIG9wdHMgPSB7fSkge1xuICB2YWxpZGF0ZUFzc2V0VHlwZShhc3NldFR5cGUpO1xuICBsZXQgbWV0YSA9IG9wdHMuZXh0cmFjdCB8fCB2b2lkIDA7XG4gIG1ldGEgJiYgIW1ldGEubGVuZ3RoICYmIChtZXRhID0gW1wibm9uZVwiXSk7XG4gIGNvbnN0IGNvbmZpZyA9IGNsaWVudC5jb25maWcoKSwgb3B0aW9ucyA9IG9wdGlvbnNGcm9tRmlsZShvcHRzLCBib2R5KSwgeyB0YWcsIGxhYmVsLCB0aXRsZSwgZGVzY3JpcHRpb24sIGNyZWRpdExpbmUsIGZpbGVuYW1lLCBzb3VyY2UgfSA9IG9wdGlvbnMsIHF1ZXJ5ID0ge1xuICAgIGxhYmVsLFxuICAgIHRpdGxlLFxuICAgIGRlc2NyaXB0aW9uLFxuICAgIGZpbGVuYW1lLFxuICAgIG1ldGEsXG4gICAgY3JlZGl0TGluZVxuICB9O1xuICByZXR1cm4gc291cmNlICYmIChxdWVyeS5zb3VyY2VJZCA9IHNvdXJjZS5pZCwgcXVlcnkuc291cmNlTmFtZSA9IHNvdXJjZS5uYW1lLCBxdWVyeS5zb3VyY2VVcmwgPSBzb3VyY2UudXJsKSwgX3JlcXVlc3RPYnNlcnZhYmxlKGNsaWVudCwgaHR0cFJlcXVlc3QsIHtcbiAgICB0YWcsXG4gICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICB0aW1lb3V0OiBvcHRpb25zLnRpbWVvdXQgfHwgMCxcbiAgICB1cmk6IGJ1aWxkQXNzZXRVcGxvYWRVcmwoY29uZmlnLCBhc3NldFR5cGUpLFxuICAgIGhlYWRlcnM6IG9wdGlvbnMuY29udGVudFR5cGUgPyB7IFwiQ29udGVudC1UeXBlXCI6IG9wdGlvbnMuY29udGVudFR5cGUgfSA6IHt9LFxuICAgIHF1ZXJ5LFxuICAgIGJvZHlcbiAgfSk7XG59XG5mdW5jdGlvbiBidWlsZEFzc2V0VXBsb2FkVXJsKGNvbmZpZywgYXNzZXRUeXBlKSB7XG4gIGNvbnN0IGFzc2V0VHlwZUVuZHBvaW50ID0gYXNzZXRUeXBlID09PSBcImltYWdlXCIgPyBcImltYWdlc1wiIDogXCJmaWxlc1wiO1xuICBpZiAoY29uZmlnW1wifmV4cGVyaW1lbnRhbF9yZXNvdXJjZVwiXSkge1xuICAgIGNvbnN0IHsgdHlwZSwgaWQgfSA9IGNvbmZpZ1tcIn5leHBlcmltZW50YWxfcmVzb3VyY2VcIl07XG4gICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICBjYXNlIFwiZGF0YXNldFwiOlxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgXCJBc3NldHMgYXJlIG5vdCBzdXBwb3J0ZWQgZm9yIGRhdGFzZXQgcmVzb3VyY2VzLCB5ZXQuIENvbmZpZ3VyZSB0aGUgY2xpZW50IHdpdGggYHtwcm9qZWN0SWQ6IDxwcm9qZWN0SWQ+LCBkYXRhc2V0OiA8ZGF0YXNldElkPn1gIGluc3RlYWQuXCJcbiAgICAgICAgKTtcbiAgICAgIGNhc2UgXCJjYW52YXNcIjpcbiAgICAgICAgcmV0dXJuIGAvY2FudmFzZXMvJHtpZH0vYXNzZXRzLyR7YXNzZXRUeXBlRW5kcG9pbnR9YDtcbiAgICAgIGNhc2UgXCJtZWRpYS1saWJyYXJ5XCI6XG4gICAgICAgIHJldHVybiBgL21lZGlhLWxpYnJhcmllcy8ke2lkfS91cGxvYWRgO1xuICAgICAgY2FzZSBcImRhc2hib2FyZFwiOlxuICAgICAgICByZXR1cm4gYC9kYXNoYm9hcmRzLyR7aWR9L2Fzc2V0cy8ke2Fzc2V0VHlwZUVuZHBvaW50fWA7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIHJlc291cmNlIHR5cGU6ICR7dHlwZS50b1N0cmluZygpfWApO1xuICAgIH1cbiAgfVxuICBjb25zdCBkYXRhc2V0MiA9IGhhc0RhdGFzZXQoY29uZmlnKTtcbiAgcmV0dXJuIGBhc3NldHMvJHthc3NldFR5cGVFbmRwb2ludH0vJHtkYXRhc2V0Mn1gO1xufVxuZnVuY3Rpb24gb3B0aW9uc0Zyb21GaWxlKG9wdHMsIGZpbGUpIHtcbiAgcmV0dXJuIHR5cGVvZiBGaWxlID4gXCJ1XCIgfHwgIShmaWxlIGluc3RhbmNlb2YgRmlsZSkgPyBvcHRzIDogT2JqZWN0LmFzc2lnbihcbiAgICB7XG4gICAgICBmaWxlbmFtZTogb3B0cy5wcmVzZXJ2ZUZpbGVuYW1lID09PSAhMSA/IHZvaWQgMCA6IGZpbGUubmFtZSxcbiAgICAgIGNvbnRlbnRUeXBlOiBmaWxlLnR5cGVcbiAgICB9LFxuICAgIG9wdHNcbiAgKTtcbn1cbnZhciBkZWZhdWx0cyA9IChvYmosIGRlZmF1bHRzMikgPT4gT2JqZWN0LmtleXMoZGVmYXVsdHMyKS5jb25jYXQoT2JqZWN0LmtleXMob2JqKSkucmVkdWNlKCh0YXJnZXQsIHByb3ApID0+ICh0YXJnZXRbcHJvcF0gPSB0eXBlb2Ygb2JqW3Byb3BdID4gXCJ1XCIgPyBkZWZhdWx0czJbcHJvcF0gOiBvYmpbcHJvcF0sIHRhcmdldCksIHt9KTtcbmNvbnN0IHBpY2sgPSAob2JqLCBwcm9wcykgPT4gcHJvcHMucmVkdWNlKChzZWxlY3Rpb24sIHByb3ApID0+ICh0eXBlb2Ygb2JqW3Byb3BdID4gXCJ1XCIgfHwgKHNlbGVjdGlvbltwcm9wXSA9IG9ialtwcm9wXSksIHNlbGVjdGlvbiksIHt9KSwgZXZlbnRTb3VyY2VQb2x5ZmlsbCA9IGRlZmVyKCgpID0+IGltcG9ydChcIkBzYW5pdHkvZXZlbnRzb3VyY2VcIikpLnBpcGUoXG4gIG1hcCgoeyBkZWZhdWx0OiBFdmVudFNvdXJjZTIgfSkgPT4gRXZlbnRTb3VyY2UyKSxcbiAgc2hhcmVSZXBsYXkoMSlcbik7XG5mdW5jdGlvbiByZWNvbm5lY3RPbkNvbm5lY3Rpb25GYWlsdXJlKCkge1xuICByZXR1cm4gZnVuY3Rpb24oc291cmNlKSB7XG4gICAgcmV0dXJuIHNvdXJjZS5waXBlKFxuICAgICAgY2F0Y2hFcnJvcigoZXJyLCBjYXVnaHQpID0+IGVyciBpbnN0YW5jZW9mIENvbm5lY3Rpb25GYWlsZWRFcnJvciA/IGNvbmNhdChvZih7IHR5cGU6IFwicmVjb25uZWN0XCIgfSksIHRpbWVyKDFlMykucGlwZShtZXJnZU1hcCgoKSA9PiBjYXVnaHQpKSkgOiB0aHJvd0Vycm9yKCgpID0+IGVycikpXG4gICAgKTtcbiAgfTtcbn1cbmNvbnN0IE1BWF9VUkxfTEVOR1RIID0gMTQ4MDAsIHBvc3NpYmxlT3B0aW9ucyA9IFtcbiAgXCJpbmNsdWRlUHJldmlvdXNSZXZpc2lvblwiLFxuICBcImluY2x1ZGVSZXN1bHRcIixcbiAgXCJpbmNsdWRlTXV0YXRpb25zXCIsXG4gIFwiaW5jbHVkZUFsbFZlcnNpb25zXCIsXG4gIFwidmlzaWJpbGl0eVwiLFxuICBcImVmZmVjdEZvcm1hdFwiLFxuICBcInRhZ1wiXG5dLCBkZWZhdWx0T3B0aW9ucyA9IHtcbiAgaW5jbHVkZVJlc3VsdDogITBcbn07XG5mdW5jdGlvbiBfbGlzdGVuKHF1ZXJ5LCBwYXJhbXMsIG9wdHMgPSB7fSkge1xuICBjb25zdCB7IHVybCwgdG9rZW4sIHdpdGhDcmVkZW50aWFscywgcmVxdWVzdFRhZ1ByZWZpeCB9ID0gdGhpcy5jb25maWcoKSwgdGFnID0gb3B0cy50YWcgJiYgcmVxdWVzdFRhZ1ByZWZpeCA/IFtyZXF1ZXN0VGFnUHJlZml4LCBvcHRzLnRhZ10uam9pbihcIi5cIikgOiBvcHRzLnRhZywgb3B0aW9ucyA9IHsgLi4uZGVmYXVsdHMob3B0cywgZGVmYXVsdE9wdGlvbnMpLCB0YWcgfSwgbGlzdGVuT3B0cyA9IHBpY2sob3B0aW9ucywgcG9zc2libGVPcHRpb25zKSwgcXMgPSBlbmNvZGVRdWVyeVN0cmluZyh7IHF1ZXJ5LCBwYXJhbXMsIG9wdGlvbnM6IHsgdGFnLCAuLi5saXN0ZW5PcHRzIH0gfSksIHVyaSA9IGAke3VybH0ke19nZXREYXRhVXJsKHRoaXMsIFwibGlzdGVuXCIsIHFzKX1gO1xuICBpZiAodXJpLmxlbmd0aCA+IE1BWF9VUkxfTEVOR1RIKVxuICAgIHJldHVybiB0aHJvd0Vycm9yKCgpID0+IG5ldyBFcnJvcihcIlF1ZXJ5IHRvbyBsYXJnZSBmb3IgbGlzdGVuZXJcIikpO1xuICBjb25zdCBsaXN0ZW5Gb3IgPSBvcHRpb25zLmV2ZW50cyA/IG9wdGlvbnMuZXZlbnRzIDogW1wibXV0YXRpb25cIl0sIGVzT3B0aW9ucyA9IHt9O1xuICByZXR1cm4gd2l0aENyZWRlbnRpYWxzICYmIChlc09wdGlvbnMud2l0aENyZWRlbnRpYWxzID0gITApLCB0b2tlbiAmJiAoZXNPcHRpb25zLmhlYWRlcnMgPSB7XG4gICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcbiAgfSksIGNvbm5lY3RFdmVudFNvdXJjZSgoKSA9PiAoXG4gICAgLy8gdXNlIHBvbHlmaWxsIGlmIHRoZXJlIGlzIG5vIGdsb2JhbCBFdmVudFNvdXJjZSBvciBpZiB3ZSBuZWVkIHRvIHNldCBoZWFkZXJzXG4gICAgKHR5cGVvZiBFdmVudFNvdXJjZSA+IFwidVwiIHx8IGVzT3B0aW9ucy5oZWFkZXJzID8gZXZlbnRTb3VyY2VQb2x5ZmlsbCA6IG9mKEV2ZW50U291cmNlKSkucGlwZShtYXAoKEV2ZW50U291cmNlMikgPT4gbmV3IEV2ZW50U291cmNlMih1cmksIGVzT3B0aW9ucykpKVxuICApLCBsaXN0ZW5Gb3IpLnBpcGUoXG4gICAgcmVjb25uZWN0T25Db25uZWN0aW9uRmFpbHVyZSgpLFxuICAgIGZpbHRlcigoZXZlbnQpID0+IGxpc3RlbkZvci5pbmNsdWRlcyhldmVudC50eXBlKSksXG4gICAgbWFwKFxuICAgICAgKGV2ZW50KSA9PiAoe1xuICAgICAgICB0eXBlOiBldmVudC50eXBlLFxuICAgICAgICAuLi5cImRhdGFcIiBpbiBldmVudCA/IGV2ZW50LmRhdGEgOiB7fVxuICAgICAgfSlcbiAgICApXG4gICk7XG59XG5mdW5jdGlvbiBzaGFyZVJlcGxheUxhdGVzdChjb25maWdPclByZWRpY2F0ZSwgY29uZmlnKSB7XG4gIHJldHVybiBfc2hhcmVSZXBsYXlMYXRlc3QoXG4gICAgdHlwZW9mIGNvbmZpZ09yUHJlZGljYXRlID09IFwiZnVuY3Rpb25cIiA/IHsgcHJlZGljYXRlOiBjb25maWdPclByZWRpY2F0ZSwgLi4uY29uZmlnIH0gOiBjb25maWdPclByZWRpY2F0ZVxuICApO1xufVxuZnVuY3Rpb24gX3NoYXJlUmVwbGF5TGF0ZXN0KGNvbmZpZykge1xuICByZXR1cm4gKHNvdXJjZSkgPT4ge1xuICAgIGxldCBsYXRlc3QsIGVtaXR0ZWQgPSAhMTtcbiAgICBjb25zdCB7IHByZWRpY2F0ZSwgLi4uc2hhcmVDb25maWcgfSA9IGNvbmZpZywgd3JhcHBlZCA9IHNvdXJjZS5waXBlKFxuICAgICAgdGFwKCh2YWx1ZSkgPT4ge1xuICAgICAgICBjb25maWcucHJlZGljYXRlKHZhbHVlKSAmJiAoZW1pdHRlZCA9ICEwLCBsYXRlc3QgPSB2YWx1ZSk7XG4gICAgICB9KSxcbiAgICAgIGZpbmFsaXplKCgpID0+IHtcbiAgICAgICAgZW1pdHRlZCA9ICExLCBsYXRlc3QgPSB2b2lkIDA7XG4gICAgICB9KSxcbiAgICAgIHNoYXJlKHNoYXJlQ29uZmlnKVxuICAgICksIGVtaXRMYXRlc3QgPSBuZXcgT2JzZXJ2YWJsZSgoc3Vic2NyaWJlcikgPT4ge1xuICAgICAgZW1pdHRlZCAmJiBzdWJzY3JpYmVyLm5leHQoXG4gICAgICAgIC8vIHRoaXMgY2FzdCBpcyBzYWZlIGJlY2F1c2Ugb2YgdGhlIGVtaXR0ZWQgY2hlY2sgd2hpY2ggYXNzZXJ0cyB0aGF0IHdlIGdvdCBUIGZyb20gdGhlIHNvdXJjZVxuICAgICAgICBsYXRlc3RcbiAgICAgICksIHN1YnNjcmliZXIuY29tcGxldGUoKTtcbiAgICB9KTtcbiAgICByZXR1cm4gbWVyZ2Uod3JhcHBlZCwgZW1pdExhdGVzdCk7XG4gIH07XG59XG5jb25zdCByZXF1aXJlZEFwaVZlcnNpb24gPSBcIjIwMjEtMDMtMjVcIjtcbmNsYXNzIExpdmVDbGllbnQge1xuICAjY2xpZW50O1xuICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICB0aGlzLiNjbGllbnQgPSBjbGllbnQ7XG4gIH1cbiAgLyoqXG4gICAqIFJlcXVpcmVzIGBhcGlWZXJzaW9uYCB0byBiZSBgMjAyMS0wMy0yNWAgb3IgbGF0ZXIuXG4gICAqL1xuICBldmVudHMoe1xuICAgIGluY2x1ZGVEcmFmdHMgPSAhMSxcbiAgICB0YWc6IF90YWdcbiAgfSA9IHt9KSB7XG4gICAgcmVzb3VyY2VHdWFyZChcImxpdmVcIiwgdGhpcy4jY2xpZW50LmNvbmZpZygpKTtcbiAgICBjb25zdCB7XG4gICAgICBwcm9qZWN0SWQsXG4gICAgICBhcGlWZXJzaW9uOiBfYXBpVmVyc2lvbixcbiAgICAgIHRva2VuLFxuICAgICAgd2l0aENyZWRlbnRpYWxzLFxuICAgICAgcmVxdWVzdFRhZ1ByZWZpeFxuICAgIH0gPSB0aGlzLiNjbGllbnQuY29uZmlnKCksIGFwaVZlcnNpb24gPSBfYXBpVmVyc2lvbi5yZXBsYWNlKC9edi8sIFwiXCIpO1xuICAgIGlmIChhcGlWZXJzaW9uICE9PSBcIlhcIiAmJiBhcGlWZXJzaW9uIDwgcmVxdWlyZWRBcGlWZXJzaW9uKVxuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBgVGhlIGxpdmUgZXZlbnRzIEFQSSByZXF1aXJlcyBBUEkgdmVyc2lvbiAke3JlcXVpcmVkQXBpVmVyc2lvbn0gb3IgbGF0ZXIuIFRoZSBjdXJyZW50IEFQSSB2ZXJzaW9uIGlzICR7YXBpVmVyc2lvbn0uIFBsZWFzZSB1cGRhdGUgeW91ciBBUEkgdmVyc2lvbiB0byB1c2UgdGhpcyBmZWF0dXJlLmBcbiAgICAgICk7XG4gICAgaWYgKGluY2x1ZGVEcmFmdHMgJiYgIXRva2VuICYmICF3aXRoQ3JlZGVudGlhbHMpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIFwiVGhlIGxpdmUgZXZlbnRzIEFQSSByZXF1aXJlcyBhIHRva2VuIG9yIHdpdGhDcmVkZW50aWFscyB3aGVuICdpbmNsdWRlRHJhZnRzOiB0cnVlJy4gUGxlYXNlIHVwZGF0ZSB5b3VyIGNsaWVudCBjb25maWd1cmF0aW9uLiBUaGUgdG9rZW4gc2hvdWxkIGhhdmUgdGhlIGxvd2VzdCBwb3NzaWJsZSBhY2Nlc3Mgcm9sZS5cIlxuICAgICAgKTtcbiAgICBjb25zdCBwYXRoID0gX2dldERhdGFVcmwodGhpcy4jY2xpZW50LCBcImxpdmUvZXZlbnRzXCIpLCB1cmwgPSBuZXcgVVJMKHRoaXMuI2NsaWVudC5nZXRVcmwocGF0aCwgITEpKSwgdGFnID0gX3RhZyAmJiByZXF1ZXN0VGFnUHJlZml4ID8gW3JlcXVlc3RUYWdQcmVmaXgsIF90YWddLmpvaW4oXCIuXCIpIDogX3RhZztcbiAgICB0YWcgJiYgdXJsLnNlYXJjaFBhcmFtcy5zZXQoXCJ0YWdcIiwgdGFnKSwgaW5jbHVkZURyYWZ0cyAmJiB1cmwuc2VhcmNoUGFyYW1zLnNldChcImluY2x1ZGVEcmFmdHNcIiwgXCJ0cnVlXCIpO1xuICAgIGNvbnN0IGVzT3B0aW9ucyA9IHt9O1xuICAgIGluY2x1ZGVEcmFmdHMgJiYgdG9rZW4gJiYgKGVzT3B0aW9ucy5oZWFkZXJzID0ge1xuICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcbiAgICB9KSwgaW5jbHVkZURyYWZ0cyAmJiB3aXRoQ3JlZGVudGlhbHMgJiYgKGVzT3B0aW9ucy53aXRoQ3JlZGVudGlhbHMgPSAhMCk7XG4gICAgY29uc3Qga2V5ID0gYCR7dXJsLmhyZWZ9Ojoke0pTT04uc3RyaW5naWZ5KGVzT3B0aW9ucyl9YCwgZXhpc3RpbmcgPSBldmVudHNDYWNoZS5nZXQoa2V5KTtcbiAgICBpZiAoZXhpc3RpbmcpXG4gICAgICByZXR1cm4gZXhpc3Rpbmc7XG4gICAgY29uc3QgZXZlbnRzID0gY29ubmVjdEV2ZW50U291cmNlKCgpID0+IChcbiAgICAgIC8vIHVzZSBwb2x5ZmlsbCBpZiB0aGVyZSBpcyBubyBnbG9iYWwgRXZlbnRTb3VyY2Ugb3IgaWYgd2UgbmVlZCB0byBzZXQgaGVhZGVyc1xuICAgICAgKHR5cGVvZiBFdmVudFNvdXJjZSA+IFwidVwiIHx8IGVzT3B0aW9ucy5oZWFkZXJzID8gZXZlbnRTb3VyY2VQb2x5ZmlsbCA6IG9mKEV2ZW50U291cmNlKSkucGlwZShtYXAoKEV2ZW50U291cmNlMikgPT4gbmV3IEV2ZW50U291cmNlMih1cmwuaHJlZiwgZXNPcHRpb25zKSkpXG4gICAgKSwgW1xuICAgICAgXCJtZXNzYWdlXCIsXG4gICAgICBcInJlc3RhcnRcIixcbiAgICAgIFwid2VsY29tZVwiLFxuICAgICAgXCJyZWNvbm5lY3RcIixcbiAgICAgIFwiZ29hd2F5XCJcbiAgICBdKS5waXBlKFxuICAgICAgcmVjb25uZWN0T25Db25uZWN0aW9uRmFpbHVyZSgpLFxuICAgICAgbWFwKChldmVudCkgPT4ge1xuICAgICAgICBpZiAoZXZlbnQudHlwZSA9PT0gXCJtZXNzYWdlXCIpIHtcbiAgICAgICAgICBjb25zdCB7IGRhdGEsIC4uLnJlc3QgfSA9IGV2ZW50O1xuICAgICAgICAgIHJldHVybiB7IC4uLnJlc3QsIHRhZ3M6IGRhdGEudGFncyB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBldmVudDtcbiAgICAgIH0pXG4gICAgKSwgY2hlY2tDb3JzID0gZmV0Y2hPYnNlcnZhYmxlKHVybCwge1xuICAgICAgbWV0aG9kOiBcIk9QVElPTlNcIixcbiAgICAgIG1vZGU6IFwiY29yc1wiLFxuICAgICAgY3JlZGVudGlhbHM6IGVzT3B0aW9ucy53aXRoQ3JlZGVudGlhbHMgPyBcImluY2x1ZGVcIiA6IFwib21pdFwiLFxuICAgICAgaGVhZGVyczogZXNPcHRpb25zLmhlYWRlcnNcbiAgICB9KS5waXBlKFxuICAgICAgbWVyZ2VNYXAoKCkgPT4gRU1QVFkpLFxuICAgICAgY2F0Y2hFcnJvcigoKSA9PiB7XG4gICAgICAgIHRocm93IG5ldyBDb3JzT3JpZ2luRXJyb3IoeyBwcm9qZWN0SWQgfSk7XG4gICAgICB9KVxuICAgICksIG9ic2VydmFibGUyID0gY29uY2F0KGNoZWNrQ29ycywgZXZlbnRzKS5waXBlKFxuICAgICAgZmluYWxpemUkMSgoKSA9PiBldmVudHNDYWNoZS5kZWxldGUoa2V5KSksXG4gICAgICBzaGFyZVJlcGxheUxhdGVzdCh7XG4gICAgICAgIHByZWRpY2F0ZTogKGV2ZW50KSA9PiBldmVudC50eXBlID09PSBcIndlbGNvbWVcIlxuICAgICAgfSlcbiAgICApO1xuICAgIHJldHVybiBldmVudHNDYWNoZS5zZXQoa2V5LCBvYnNlcnZhYmxlMiksIG9ic2VydmFibGUyO1xuICB9XG59XG5mdW5jdGlvbiBmZXRjaE9ic2VydmFibGUodXJsLCBpbml0KSB7XG4gIHJldHVybiBuZXcgT2JzZXJ2YWJsZSgob2JzZXJ2ZXIpID0+IHtcbiAgICBjb25zdCBjb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpLCBzaWduYWwgPSBjb250cm9sbGVyLnNpZ25hbDtcbiAgICByZXR1cm4gZmV0Y2godXJsLCB7IC4uLmluaXQsIHNpZ25hbDogY29udHJvbGxlci5zaWduYWwgfSkudGhlbihcbiAgICAgIChyZXNwb25zZSkgPT4ge1xuICAgICAgICBvYnNlcnZlci5uZXh0KHJlc3BvbnNlKSwgb2JzZXJ2ZXIuY29tcGxldGUoKTtcbiAgICAgIH0sXG4gICAgICAoZXJyKSA9PiB7XG4gICAgICAgIHNpZ25hbC5hYm9ydGVkIHx8IG9ic2VydmVyLmVycm9yKGVycik7XG4gICAgICB9XG4gICAgKSwgKCkgPT4gY29udHJvbGxlci5hYm9ydCgpO1xuICB9KTtcbn1cbmNvbnN0IGV2ZW50c0NhY2hlID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTtcbmNsYXNzIE9ic2VydmFibGVEYXRhc2V0c0NsaWVudCB7XG4gICNjbGllbnQ7XG4gICNodHRwUmVxdWVzdDtcbiAgY29uc3RydWN0b3IoY2xpZW50LCBodHRwUmVxdWVzdCkge1xuICAgIHRoaXMuI2NsaWVudCA9IGNsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QgPSBodHRwUmVxdWVzdDtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IGRhdGFzZXQgd2l0aCB0aGUgZ2l2ZW4gbmFtZVxuICAgKlxuICAgKiBAcGFyYW0gbmFtZSAtIE5hbWUgb2YgdGhlIGRhdGFzZXQgdG8gY3JlYXRlXG4gICAqIEBwYXJhbSBvcHRpb25zIC0gT3B0aW9ucyBmb3IgdGhlIGRhdGFzZXRcbiAgICovXG4gIGNyZWF0ZShuYW1lMiwgb3B0aW9ucykge1xuICAgIHJldHVybiBfbW9kaWZ5KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIFwiUFVUXCIsIG5hbWUyLCBvcHRpb25zKTtcbiAgfVxuICAvKipcbiAgICogRWRpdCBhIGRhdGFzZXQgd2l0aCB0aGUgZ2l2ZW4gbmFtZVxuICAgKlxuICAgKiBAcGFyYW0gbmFtZSAtIE5hbWUgb2YgdGhlIGRhdGFzZXQgdG8gZWRpdFxuICAgKiBAcGFyYW0gb3B0aW9ucyAtIE5ldyBvcHRpb25zIGZvciB0aGUgZGF0YXNldFxuICAgKi9cbiAgZWRpdChuYW1lMiwgb3B0aW9ucykge1xuICAgIHJldHVybiBfbW9kaWZ5KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIFwiUEFUQ0hcIiwgbmFtZTIsIG9wdGlvbnMpO1xuICB9XG4gIC8qKlxuICAgKiBEZWxldGUgYSBkYXRhc2V0IHdpdGggdGhlIGdpdmVuIG5hbWVcbiAgICpcbiAgICogQHBhcmFtIG5hbWUgLSBOYW1lIG9mIHRoZSBkYXRhc2V0IHRvIGRlbGV0ZVxuICAgKi9cbiAgZGVsZXRlKG5hbWUyKSB7XG4gICAgcmV0dXJuIF9tb2RpZnkodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwgXCJERUxFVEVcIiwgbmFtZTIpO1xuICB9XG4gIC8qKlxuICAgKiBGZXRjaCBhIGxpc3Qgb2YgZGF0YXNldHMgZm9yIHRoZSBjb25maWd1cmVkIHByb2plY3RcbiAgICovXG4gIGxpc3QoKSB7XG4gICAgcmV0dXJuIF9yZXF1ZXN0KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIHtcbiAgICAgIHVyaTogXCIvZGF0YXNldHNcIixcbiAgICAgIHRhZzogbnVsbFxuICAgIH0pO1xuICB9XG59XG5jbGFzcyBEYXRhc2V0c0NsaWVudCB7XG4gICNjbGllbnQ7XG4gICNodHRwUmVxdWVzdDtcbiAgY29uc3RydWN0b3IoY2xpZW50LCBodHRwUmVxdWVzdCkge1xuICAgIHRoaXMuI2NsaWVudCA9IGNsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QgPSBodHRwUmVxdWVzdDtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IGRhdGFzZXQgd2l0aCB0aGUgZ2l2ZW4gbmFtZVxuICAgKlxuICAgKiBAcGFyYW0gbmFtZSAtIE5hbWUgb2YgdGhlIGRhdGFzZXQgdG8gY3JlYXRlXG4gICAqIEBwYXJhbSBvcHRpb25zIC0gT3B0aW9ucyBmb3IgdGhlIGRhdGFzZXRcbiAgICovXG4gIGNyZWF0ZShuYW1lMiwgb3B0aW9ucykge1xuICAgIHJldHVybiByZXNvdXJjZUd1YXJkKFwiZGF0YXNldFwiLCB0aGlzLiNjbGllbnQuY29uZmlnKCkpLCBsYXN0VmFsdWVGcm9tKFxuICAgICAgX21vZGlmeSh0aGlzLiNjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0LCBcIlBVVFwiLCBuYW1lMiwgb3B0aW9ucylcbiAgICApO1xuICB9XG4gIC8qKlxuICAgKiBFZGl0IGEgZGF0YXNldCB3aXRoIHRoZSBnaXZlbiBuYW1lXG4gICAqXG4gICAqIEBwYXJhbSBuYW1lIC0gTmFtZSBvZiB0aGUgZGF0YXNldCB0byBlZGl0XG4gICAqIEBwYXJhbSBvcHRpb25zIC0gTmV3IG9wdGlvbnMgZm9yIHRoZSBkYXRhc2V0XG4gICAqL1xuICBlZGl0KG5hbWUyLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIHJlc291cmNlR3VhcmQoXCJkYXRhc2V0XCIsIHRoaXMuI2NsaWVudC5jb25maWcoKSksIGxhc3RWYWx1ZUZyb20oXG4gICAgICBfbW9kaWZ5KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIFwiUEFUQ0hcIiwgbmFtZTIsIG9wdGlvbnMpXG4gICAgKTtcbiAgfVxuICAvKipcbiAgICogRGVsZXRlIGEgZGF0YXNldCB3aXRoIHRoZSBnaXZlbiBuYW1lXG4gICAqXG4gICAqIEBwYXJhbSBuYW1lIC0gTmFtZSBvZiB0aGUgZGF0YXNldCB0byBkZWxldGVcbiAgICovXG4gIGRlbGV0ZShuYW1lMikge1xuICAgIHJldHVybiByZXNvdXJjZUd1YXJkKFwiZGF0YXNldFwiLCB0aGlzLiNjbGllbnQuY29uZmlnKCkpLCBsYXN0VmFsdWVGcm9tKF9tb2RpZnkodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwgXCJERUxFVEVcIiwgbmFtZTIpKTtcbiAgfVxuICAvKipcbiAgICogRmV0Y2ggYSBsaXN0IG9mIGRhdGFzZXRzIGZvciB0aGUgY29uZmlndXJlZCBwcm9qZWN0XG4gICAqL1xuICBsaXN0KCkge1xuICAgIHJldHVybiByZXNvdXJjZUd1YXJkKFwiZGF0YXNldFwiLCB0aGlzLiNjbGllbnQuY29uZmlnKCkpLCBsYXN0VmFsdWVGcm9tKFxuICAgICAgX3JlcXVlc3QodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwgeyB1cmk6IFwiL2RhdGFzZXRzXCIsIHRhZzogbnVsbCB9KVxuICAgICk7XG4gIH1cbn1cbmZ1bmN0aW9uIF9tb2RpZnkoY2xpZW50LCBodHRwUmVxdWVzdCwgbWV0aG9kLCBuYW1lMiwgb3B0aW9ucykge1xuICByZXR1cm4gcmVzb3VyY2VHdWFyZChcImRhdGFzZXRcIiwgY2xpZW50LmNvbmZpZygpKSwgZGF0YXNldChuYW1lMiksIF9yZXF1ZXN0KGNsaWVudCwgaHR0cFJlcXVlc3QsIHtcbiAgICBtZXRob2QsXG4gICAgdXJpOiBgL2RhdGFzZXRzLyR7bmFtZTJ9YCxcbiAgICBib2R5OiBvcHRpb25zLFxuICAgIHRhZzogbnVsbFxuICB9KTtcbn1cbmNsYXNzIE9ic2VydmFibGVQcm9qZWN0c0NsaWVudCB7XG4gICNjbGllbnQ7XG4gICNodHRwUmVxdWVzdDtcbiAgY29uc3RydWN0b3IoY2xpZW50LCBodHRwUmVxdWVzdCkge1xuICAgIHRoaXMuI2NsaWVudCA9IGNsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QgPSBodHRwUmVxdWVzdDtcbiAgfVxuICBsaXN0KG9wdGlvbnMpIHtcbiAgICByZXNvdXJjZUd1YXJkKFwicHJvamVjdHNcIiwgdGhpcy4jY2xpZW50LmNvbmZpZygpKTtcbiAgICBjb25zdCB1cmkgPSBvcHRpb25zPy5pbmNsdWRlTWVtYmVycyA9PT0gITEgPyBcIi9wcm9qZWN0cz9pbmNsdWRlTWVtYmVycz1mYWxzZVwiIDogXCIvcHJvamVjdHNcIjtcbiAgICByZXR1cm4gX3JlcXVlc3QodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwgeyB1cmkgfSk7XG4gIH1cbiAgLyoqXG4gICAqIEZldGNoIGEgcHJvamVjdCBieSBwcm9qZWN0IElEXG4gICAqXG4gICAqIEBwYXJhbSBwcm9qZWN0SWQgLSBJRCBvZiB0aGUgcHJvamVjdCB0byBmZXRjaFxuICAgKi9cbiAgZ2V0QnlJZChwcm9qZWN0SWQpIHtcbiAgICByZXR1cm4gcmVzb3VyY2VHdWFyZChcInByb2plY3RzXCIsIHRoaXMuI2NsaWVudC5jb25maWcoKSksIF9yZXF1ZXN0KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIHsgdXJpOiBgL3Byb2plY3RzLyR7cHJvamVjdElkfWAgfSk7XG4gIH1cbn1cbmNsYXNzIFByb2plY3RzQ2xpZW50IHtcbiAgI2NsaWVudDtcbiAgI2h0dHBSZXF1ZXN0O1xuICBjb25zdHJ1Y3RvcihjbGllbnQsIGh0dHBSZXF1ZXN0KSB7XG4gICAgdGhpcy4jY2xpZW50ID0gY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCA9IGh0dHBSZXF1ZXN0O1xuICB9XG4gIGxpc3Qob3B0aW9ucykge1xuICAgIHJlc291cmNlR3VhcmQoXCJwcm9qZWN0c1wiLCB0aGlzLiNjbGllbnQuY29uZmlnKCkpO1xuICAgIGNvbnN0IHVyaSA9IG9wdGlvbnM/LmluY2x1ZGVNZW1iZXJzID09PSAhMSA/IFwiL3Byb2plY3RzP2luY2x1ZGVNZW1iZXJzPWZhbHNlXCIgOiBcIi9wcm9qZWN0c1wiO1xuICAgIHJldHVybiBsYXN0VmFsdWVGcm9tKF9yZXF1ZXN0KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIHsgdXJpIH0pKTtcbiAgfVxuICAvKipcbiAgICogRmV0Y2ggYSBwcm9qZWN0IGJ5IHByb2plY3QgSURcbiAgICpcbiAgICogQHBhcmFtIHByb2plY3RJZCAtIElEIG9mIHRoZSBwcm9qZWN0IHRvIGZldGNoXG4gICAqL1xuICBnZXRCeUlkKHByb2plY3RJZCkge1xuICAgIHJldHVybiByZXNvdXJjZUd1YXJkKFwicHJvamVjdHNcIiwgdGhpcy4jY2xpZW50LmNvbmZpZygpKSwgbGFzdFZhbHVlRnJvbShcbiAgICAgIF9yZXF1ZXN0KHRoaXMuI2NsaWVudCwgdGhpcy4jaHR0cFJlcXVlc3QsIHsgdXJpOiBgL3Byb2plY3RzLyR7cHJvamVjdElkfWAgfSlcbiAgICApO1xuICB9XG59XG5jbGFzcyBPYnNlcnZhYmxlVXNlcnNDbGllbnQge1xuICAjY2xpZW50O1xuICAjaHR0cFJlcXVlc3Q7XG4gIGNvbnN0cnVjdG9yKGNsaWVudCwgaHR0cFJlcXVlc3QpIHtcbiAgICB0aGlzLiNjbGllbnQgPSBjbGllbnQsIHRoaXMuI2h0dHBSZXF1ZXN0ID0gaHR0cFJlcXVlc3Q7XG4gIH1cbiAgLyoqXG4gICAqIEZldGNoIGEgdXNlciBieSB1c2VyIElEXG4gICAqXG4gICAqIEBwYXJhbSBpZCAtIFVzZXIgSUQgb2YgdGhlIHVzZXIgdG8gZmV0Y2guIElmIGBtZWAgaXMgcHJvdmlkZWQsIGEgbWluaW1hbCByZXNwb25zZSBpbmNsdWRpbmcgdGhlIHVzZXJzIHJvbGUgaXMgcmV0dXJuZWQuXG4gICAqL1xuICBnZXRCeUlkKGlkKSB7XG4gICAgcmV0dXJuIF9yZXF1ZXN0KFxuICAgICAgdGhpcy4jY2xpZW50LFxuICAgICAgdGhpcy4jaHR0cFJlcXVlc3QsXG4gICAgICB7IHVyaTogYC91c2Vycy8ke2lkfWAgfVxuICAgICk7XG4gIH1cbn1cbmNsYXNzIFVzZXJzQ2xpZW50IHtcbiAgI2NsaWVudDtcbiAgI2h0dHBSZXF1ZXN0O1xuICBjb25zdHJ1Y3RvcihjbGllbnQsIGh0dHBSZXF1ZXN0KSB7XG4gICAgdGhpcy4jY2xpZW50ID0gY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCA9IGh0dHBSZXF1ZXN0O1xuICB9XG4gIC8qKlxuICAgKiBGZXRjaCBhIHVzZXIgYnkgdXNlciBJRFxuICAgKlxuICAgKiBAcGFyYW0gaWQgLSBVc2VyIElEIG9mIHRoZSB1c2VyIHRvIGZldGNoLiBJZiBgbWVgIGlzIHByb3ZpZGVkLCBhIG1pbmltYWwgcmVzcG9uc2UgaW5jbHVkaW5nIHRoZSB1c2VycyByb2xlIGlzIHJldHVybmVkLlxuICAgKi9cbiAgZ2V0QnlJZChpZCkge1xuICAgIHJldHVybiBsYXN0VmFsdWVGcm9tKFxuICAgICAgX3JlcXVlc3QodGhpcy4jY2xpZW50LCB0aGlzLiNodHRwUmVxdWVzdCwge1xuICAgICAgICB1cmk6IGAvdXNlcnMvJHtpZH1gXG4gICAgICB9KVxuICAgICk7XG4gIH1cbn1cbmNsYXNzIE9ic2VydmFibGVTYW5pdHlDbGllbnQge1xuICBhc3NldHM7XG4gIGRhdGFzZXRzO1xuICBsaXZlO1xuICBwcm9qZWN0cztcbiAgdXNlcnM7XG4gIGFnZW50O1xuICAvKipcbiAgICogUHJpdmF0ZSBwcm9wZXJ0aWVzXG4gICAqL1xuICAjY2xpZW50Q29uZmlnO1xuICAjaHR0cFJlcXVlc3Q7XG4gIC8qKlxuICAgKiBJbnN0YW5jZSBwcm9wZXJ0aWVzXG4gICAqL1xuICBsaXN0ZW4gPSBfbGlzdGVuO1xuICBjb25zdHJ1Y3RvcihodHRwUmVxdWVzdCwgY29uZmlnID0gZGVmYXVsdENvbmZpZykge1xuICAgIHRoaXMuY29uZmlnKGNvbmZpZyksIHRoaXMuI2h0dHBSZXF1ZXN0ID0gaHR0cFJlcXVlc3QsIHRoaXMuYXNzZXRzID0gbmV3IE9ic2VydmFibGVBc3NldHNDbGllbnQodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QpLCB0aGlzLmRhdGFzZXRzID0gbmV3IE9ic2VydmFibGVEYXRhc2V0c0NsaWVudCh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCksIHRoaXMubGl2ZSA9IG5ldyBMaXZlQ2xpZW50KHRoaXMpLCB0aGlzLnByb2plY3RzID0gbmV3IE9ic2VydmFibGVQcm9qZWN0c0NsaWVudCh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCksIHRoaXMudXNlcnMgPSBuZXcgT2JzZXJ2YWJsZVVzZXJzQ2xpZW50KHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0KSwgdGhpcy5hZ2VudCA9IHtcbiAgICAgIGFjdGlvbjogbmV3IE9ic2VydmFibGVBZ2VudHNBY3Rpb25DbGllbnQodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QpXG4gICAgfTtcbiAgfVxuICAvKipcbiAgICogQ2xvbmUgdGhlIGNsaWVudCAtIHJldHVybnMgYSBuZXcgaW5zdGFuY2VcbiAgICovXG4gIGNsb25lKCkge1xuICAgIHJldHVybiBuZXcgT2JzZXJ2YWJsZVNhbml0eUNsaWVudCh0aGlzLiNodHRwUmVxdWVzdCwgdGhpcy5jb25maWcoKSk7XG4gIH1cbiAgY29uZmlnKG5ld0NvbmZpZykge1xuICAgIGlmIChuZXdDb25maWcgPT09IHZvaWQgMClcbiAgICAgIHJldHVybiB7IC4uLnRoaXMuI2NsaWVudENvbmZpZyB9O1xuICAgIGlmICh0aGlzLiNjbGllbnRDb25maWcgJiYgdGhpcy4jY2xpZW50Q29uZmlnLmFsbG93UmVjb25maWd1cmUgPT09ICExKVxuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBcIkV4aXN0aW5nIGNsaWVudCBpbnN0YW5jZSBjYW5ub3QgYmUgcmVjb25maWd1cmVkIC0gdXNlIGB3aXRoQ29uZmlnKG5ld0NvbmZpZylgIHRvIHJldHVybiBhIG5ldyBjbGllbnRcIlxuICAgICAgKTtcbiAgICByZXR1cm4gdGhpcy4jY2xpZW50Q29uZmlnID0gaW5pdENvbmZpZyhuZXdDb25maWcsIHRoaXMuI2NsaWVudENvbmZpZyB8fCB7fSksIHRoaXM7XG4gIH1cbiAgLyoqXG4gICAqIENsb25lIHRoZSBjbGllbnQgd2l0aCBhIG5ldyAocGFydGlhbCkgY29uZmlndXJhdGlvbi5cbiAgICpcbiAgICogQHBhcmFtIG5ld0NvbmZpZyAtIE5ldyBjbGllbnQgY29uZmlndXJhdGlvbiBwcm9wZXJ0aWVzLCBzaGFsbG93bHkgbWVyZ2VkIHdpdGggZXhpc3RpbmcgY29uZmlndXJhdGlvblxuICAgKi9cbiAgd2l0aENvbmZpZyhuZXdDb25maWcpIHtcbiAgICBjb25zdCB0aGlzQ29uZmlnID0gdGhpcy5jb25maWcoKTtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGVTYW5pdHlDbGllbnQodGhpcy4jaHR0cFJlcXVlc3QsIHtcbiAgICAgIC4uLnRoaXNDb25maWcsXG4gICAgICAuLi5uZXdDb25maWcsXG4gICAgICBzdGVnYToge1xuICAgICAgICAuLi50aGlzQ29uZmlnLnN0ZWdhIHx8IHt9LFxuICAgICAgICAuLi50eXBlb2YgbmV3Q29uZmlnPy5zdGVnYSA9PSBcImJvb2xlYW5cIiA/IHsgZW5hYmxlZDogbmV3Q29uZmlnLnN0ZWdhIH0gOiBuZXdDb25maWc/LnN0ZWdhIHx8IHt9XG4gICAgICB9XG4gICAgfSk7XG4gIH1cbiAgZmV0Y2gocXVlcnksIHBhcmFtcywgb3B0aW9ucykge1xuICAgIHJldHVybiBfZmV0Y2goXG4gICAgICB0aGlzLFxuICAgICAgdGhpcy4jaHR0cFJlcXVlc3QsXG4gICAgICB0aGlzLiNjbGllbnRDb25maWcuc3RlZ2EsXG4gICAgICBxdWVyeSxcbiAgICAgIHBhcmFtcyxcbiAgICAgIG9wdGlvbnNcbiAgICApO1xuICB9XG4gIC8qKlxuICAgKiBGZXRjaCBhIHNpbmdsZSBkb2N1bWVudCB3aXRoIHRoZSBnaXZlbiBJRC5cbiAgICpcbiAgICogQHBhcmFtIGlkIC0gRG9jdW1lbnQgSUQgdG8gZmV0Y2hcbiAgICogQHBhcmFtIG9wdGlvbnMgLSBSZXF1ZXN0IG9wdGlvbnNcbiAgICovXG4gIGdldERvY3VtZW50KGlkLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIF9nZXREb2N1bWVudCh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCwgaWQsIG9wdGlvbnMpO1xuICB9XG4gIC8qKlxuICAgKiBGZXRjaCBtdWx0aXBsZSBkb2N1bWVudHMgaW4gb25lIHJlcXVlc3QuXG4gICAqIFNob3VsZCBiZSB1c2VkIHNwYXJpbmdseSAtIHBlcmZvcm1pbmcgYSBxdWVyeSBpcyB1c3VhbGx5IGEgYmV0dGVyIG9wdGlvbi5cbiAgICogVGhlIG9yZGVyL3Bvc2l0aW9uIG9mIGRvY3VtZW50cyBpcyBwcmVzZXJ2ZWQgYmFzZWQgb24gdGhlIG9yaWdpbmFsIGFycmF5IG9mIElEcy5cbiAgICogSWYgYW55IG9mIHRoZSBkb2N1bWVudHMgYXJlIG1pc3NpbmcsIHRoZXkgd2lsbCBiZSByZXBsYWNlZCBieSBhIGBudWxsYCBlbnRyeSBpbiB0aGUgcmV0dXJuZWQgYXJyYXlcbiAgICpcbiAgICogQHBhcmFtIGlkcyAtIERvY3VtZW50IElEcyB0byBmZXRjaFxuICAgKiBAcGFyYW0gb3B0aW9ucyAtIFJlcXVlc3Qgb3B0aW9uc1xuICAgKi9cbiAgZ2V0RG9jdW1lbnRzKGlkcywgb3B0aW9ucykge1xuICAgIHJldHVybiBfZ2V0RG9jdW1lbnRzKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBpZHMsIG9wdGlvbnMpO1xuICB9XG4gIGNyZWF0ZShkb2N1bWVudCwgb3B0aW9ucykge1xuICAgIHJldHVybiBfY3JlYXRlKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBkb2N1bWVudCwgXCJjcmVhdGVcIiwgb3B0aW9ucyk7XG4gIH1cbiAgY3JlYXRlSWZOb3RFeGlzdHMoZG9jdW1lbnQsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gX2NyZWF0ZUlmTm90RXhpc3RzKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBkb2N1bWVudCwgb3B0aW9ucyk7XG4gIH1cbiAgY3JlYXRlT3JSZXBsYWNlKGRvY3VtZW50LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIF9jcmVhdGVPclJlcGxhY2UodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QsIGRvY3VtZW50LCBvcHRpb25zKTtcbiAgfVxuICBkZWxldGUoc2VsZWN0aW9uLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIF9kZWxldGUodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QsIHNlbGVjdGlvbiwgb3B0aW9ucyk7XG4gIH1cbiAgbXV0YXRlKG9wZXJhdGlvbnMsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gX211dGF0ZSh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCwgb3BlcmF0aW9ucywgb3B0aW9ucyk7XG4gIH1cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyBidWlsZGFibGUgcGF0Y2ggb2Ygb3BlcmF0aW9ucyB0byBwZXJmb3JtXG4gICAqXG4gICAqIEBwYXJhbSBzZWxlY3Rpb24gLSBEb2N1bWVudCBJRCwgYW4gYXJyYXkgb2YgZG9jdW1lbnQgSURzLCBvciBhbiBvYmplY3Qgd2l0aCBgcXVlcnlgIGFuZCBvcHRpb25hbCBgcGFyYW1zYCwgZGVmaW5pbmcgd2hpY2ggZG9jdW1lbnQocykgdG8gcGF0Y2hcbiAgICogQHBhcmFtIG9wZXJhdGlvbnMgLSBPcHRpb25hbCBvYmplY3Qgb2YgcGF0Y2ggb3BlcmF0aW9ucyB0byBpbml0aWFsaXplIHRoZSBwYXRjaCBpbnN0YW5jZSB3aXRoXG4gICAqIEByZXR1cm5zIFBhdGNoIGluc3RhbmNlIC0gY2FsbCBgLmNvbW1pdCgpYCB0byBwZXJmb3JtIHRoZSBvcGVyYXRpb25zIGRlZmluZWRcbiAgICovXG4gIHBhdGNoKHNlbGVjdGlvbiwgb3BlcmF0aW9ucykge1xuICAgIHJldHVybiBuZXcgT2JzZXJ2YWJsZVBhdGNoKHNlbGVjdGlvbiwgb3BlcmF0aW9ucywgdGhpcyk7XG4gIH1cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyB0cmFuc2FjdGlvbiBvZiBtdXRhdGlvbnNcbiAgICpcbiAgICogQHBhcmFtIG9wZXJhdGlvbnMgLSBPcHRpb25hbCBhcnJheSBvZiBtdXRhdGlvbiBvcGVyYXRpb25zIHRvIGluaXRpYWxpemUgdGhlIHRyYW5zYWN0aW9uIGluc3RhbmNlIHdpdGhcbiAgICovXG4gIHRyYW5zYWN0aW9uKG9wZXJhdGlvbnMpIHtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGVUcmFuc2FjdGlvbihvcGVyYXRpb25zLCB0aGlzKTtcbiAgfVxuICAvKipcbiAgICogUGVyZm9ybSBhY3Rpb24gb3BlcmF0aW9ucyBhZ2FpbnN0IHRoZSBjb25maWd1cmVkIGRhdGFzZXRcbiAgICpcbiAgICogQHBhcmFtIG9wZXJhdGlvbnMgLSBBY3Rpb24gb3BlcmF0aW9uKHMpIHRvIGV4ZWN1dGVcbiAgICogQHBhcmFtIG9wdGlvbnMgLSBBY3Rpb24gb3B0aW9uc1xuICAgKi9cbiAgYWN0aW9uKG9wZXJhdGlvbnMsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gX2FjdGlvbih0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCwgb3BlcmF0aW9ucywgb3B0aW9ucyk7XG4gIH1cbiAgLyoqXG4gICAqIFBlcmZvcm0gYW4gSFRUUCByZXF1ZXN0IGFnYWluc3QgdGhlIFNhbml0eSBBUElcbiAgICpcbiAgICogQHBhcmFtIG9wdGlvbnMgLSBSZXF1ZXN0IG9wdGlvbnNcbiAgICovXG4gIHJlcXVlc3Qob3B0aW9ucykge1xuICAgIHJldHVybiBfcmVxdWVzdCh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCwgb3B0aW9ucyk7XG4gIH1cbiAgLyoqXG4gICAqIEdldCBhIFNhbml0eSBBUEkgVVJMIGZvciB0aGUgVVJJIHByb3ZpZGVkXG4gICAqXG4gICAqIEBwYXJhbSB1cmkgLSBVUkkvcGF0aCB0byBidWlsZCBVUkwgZm9yXG4gICAqIEBwYXJhbSBjYW5Vc2VDZG4gLSBXaGV0aGVyIG9yIG5vdCB0byBhbGxvdyB1c2luZyB0aGUgQVBJIENETiBmb3IgdGhpcyByb3V0ZVxuICAgKi9cbiAgZ2V0VXJsKHVyaSwgY2FuVXNlQ2RuKSB7XG4gICAgcmV0dXJuIF9nZXRVcmwodGhpcywgdXJpLCBjYW5Vc2VDZG4pO1xuICB9XG4gIC8qKlxuICAgKiBHZXQgYSBTYW5pdHkgQVBJIFVSTCBmb3IgdGhlIGRhdGEgb3BlcmF0aW9uIGFuZCBwYXRoIHByb3ZpZGVkXG4gICAqXG4gICAqIEBwYXJhbSBvcGVyYXRpb24gLSBEYXRhIG9wZXJhdGlvbiAoZWcgYHF1ZXJ5YCwgYG11dGF0ZWAsIGBsaXN0ZW5gIG9yIHNpbWlsYXIpXG4gICAqIEBwYXJhbSBwYXRoIC0gUGF0aCB0byBhcHBlbmQgYWZ0ZXIgdGhlIG9wZXJhdGlvblxuICAgKi9cbiAgZ2V0RGF0YVVybChvcGVyYXRpb24sIHBhdGgpIHtcbiAgICByZXR1cm4gX2dldERhdGFVcmwodGhpcywgb3BlcmF0aW9uLCBwYXRoKTtcbiAgfVxufVxuY2xhc3MgU2FuaXR5Q2xpZW50IHtcbiAgYXNzZXRzO1xuICBkYXRhc2V0cztcbiAgbGl2ZTtcbiAgcHJvamVjdHM7XG4gIHVzZXJzO1xuICBhZ2VudDtcbiAgLyoqXG4gICAqIE9ic2VydmFibGUgdmVyc2lvbiBvZiB0aGUgU2FuaXR5IGNsaWVudCwgd2l0aCB0aGUgc2FtZSBjb25maWd1cmF0aW9uIGFzIHRoZSBwcm9taXNlLWJhc2VkIG9uZVxuICAgKi9cbiAgb2JzZXJ2YWJsZTtcbiAgLyoqXG4gICAqIFByaXZhdGUgcHJvcGVydGllc1xuICAgKi9cbiAgI2NsaWVudENvbmZpZztcbiAgI2h0dHBSZXF1ZXN0O1xuICAvKipcbiAgICogSW5zdGFuY2UgcHJvcGVydGllc1xuICAgKi9cbiAgbGlzdGVuID0gX2xpc3RlbjtcbiAgY29uc3RydWN0b3IoaHR0cFJlcXVlc3QsIGNvbmZpZyA9IGRlZmF1bHRDb25maWcpIHtcbiAgICB0aGlzLmNvbmZpZyhjb25maWcpLCB0aGlzLiNodHRwUmVxdWVzdCA9IGh0dHBSZXF1ZXN0LCB0aGlzLmFzc2V0cyA9IG5ldyBBc3NldHNDbGllbnQodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QpLCB0aGlzLmRhdGFzZXRzID0gbmV3IERhdGFzZXRzQ2xpZW50KHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0KSwgdGhpcy5saXZlID0gbmV3IExpdmVDbGllbnQodGhpcyksIHRoaXMucHJvamVjdHMgPSBuZXcgUHJvamVjdHNDbGllbnQodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QpLCB0aGlzLnVzZXJzID0gbmV3IFVzZXJzQ2xpZW50KHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0KSwgdGhpcy5hZ2VudCA9IHtcbiAgICAgIGFjdGlvbjogbmV3IEFnZW50QWN0aW9uc0NsaWVudCh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdClcbiAgICB9LCB0aGlzLm9ic2VydmFibGUgPSBuZXcgT2JzZXJ2YWJsZVNhbml0eUNsaWVudChodHRwUmVxdWVzdCwgY29uZmlnKTtcbiAgfVxuICAvKipcbiAgICogQ2xvbmUgdGhlIGNsaWVudCAtIHJldHVybnMgYSBuZXcgaW5zdGFuY2VcbiAgICovXG4gIGNsb25lKCkge1xuICAgIHJldHVybiBuZXcgU2FuaXR5Q2xpZW50KHRoaXMuI2h0dHBSZXF1ZXN0LCB0aGlzLmNvbmZpZygpKTtcbiAgfVxuICBjb25maWcobmV3Q29uZmlnKSB7XG4gICAgaWYgKG5ld0NvbmZpZyA9PT0gdm9pZCAwKVxuICAgICAgcmV0dXJuIHsgLi4udGhpcy4jY2xpZW50Q29uZmlnIH07XG4gICAgaWYgKHRoaXMuI2NsaWVudENvbmZpZyAmJiB0aGlzLiNjbGllbnRDb25maWcuYWxsb3dSZWNvbmZpZ3VyZSA9PT0gITEpXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgIFwiRXhpc3RpbmcgY2xpZW50IGluc3RhbmNlIGNhbm5vdCBiZSByZWNvbmZpZ3VyZWQgLSB1c2UgYHdpdGhDb25maWcobmV3Q29uZmlnKWAgdG8gcmV0dXJuIGEgbmV3IGNsaWVudFwiXG4gICAgICApO1xuICAgIHJldHVybiB0aGlzLm9ic2VydmFibGUgJiYgdGhpcy5vYnNlcnZhYmxlLmNvbmZpZyhuZXdDb25maWcpLCB0aGlzLiNjbGllbnRDb25maWcgPSBpbml0Q29uZmlnKG5ld0NvbmZpZywgdGhpcy4jY2xpZW50Q29uZmlnIHx8IHt9KSwgdGhpcztcbiAgfVxuICAvKipcbiAgICogQ2xvbmUgdGhlIGNsaWVudCB3aXRoIGEgbmV3IChwYXJ0aWFsKSBjb25maWd1cmF0aW9uLlxuICAgKlxuICAgKiBAcGFyYW0gbmV3Q29uZmlnIC0gTmV3IGNsaWVudCBjb25maWd1cmF0aW9uIHByb3BlcnRpZXMsIHNoYWxsb3dseSBtZXJnZWQgd2l0aCBleGlzdGluZyBjb25maWd1cmF0aW9uXG4gICAqL1xuICB3aXRoQ29uZmlnKG5ld0NvbmZpZykge1xuICAgIGNvbnN0IHRoaXNDb25maWcgPSB0aGlzLmNvbmZpZygpO1xuICAgIHJldHVybiBuZXcgU2FuaXR5Q2xpZW50KHRoaXMuI2h0dHBSZXF1ZXN0LCB7XG4gICAgICAuLi50aGlzQ29uZmlnLFxuICAgICAgLi4ubmV3Q29uZmlnLFxuICAgICAgc3RlZ2E6IHtcbiAgICAgICAgLi4udGhpc0NvbmZpZy5zdGVnYSB8fCB7fSxcbiAgICAgICAgLi4udHlwZW9mIG5ld0NvbmZpZz8uc3RlZ2EgPT0gXCJib29sZWFuXCIgPyB7IGVuYWJsZWQ6IG5ld0NvbmZpZy5zdGVnYSB9IDogbmV3Q29uZmlnPy5zdGVnYSB8fCB7fVxuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIGZldGNoKHF1ZXJ5LCBwYXJhbXMsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbGFzdFZhbHVlRnJvbShcbiAgICAgIF9mZXRjaChcbiAgICAgICAgdGhpcyxcbiAgICAgICAgdGhpcy4jaHR0cFJlcXVlc3QsXG4gICAgICAgIHRoaXMuI2NsaWVudENvbmZpZy5zdGVnYSxcbiAgICAgICAgcXVlcnksXG4gICAgICAgIHBhcmFtcyxcbiAgICAgICAgb3B0aW9uc1xuICAgICAgKVxuICAgICk7XG4gIH1cbiAgLyoqXG4gICAqIEZldGNoIGEgc2luZ2xlIGRvY3VtZW50IHdpdGggdGhlIGdpdmVuIElELlxuICAgKlxuICAgKiBAcGFyYW0gaWQgLSBEb2N1bWVudCBJRCB0byBmZXRjaFxuICAgKiBAcGFyYW0gb3B0aW9ucyAtIFJlcXVlc3Qgb3B0aW9uc1xuICAgKi9cbiAgZ2V0RG9jdW1lbnQoaWQsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbGFzdFZhbHVlRnJvbShfZ2V0RG9jdW1lbnQodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QsIGlkLCBvcHRpb25zKSk7XG4gIH1cbiAgLyoqXG4gICAqIEZldGNoIG11bHRpcGxlIGRvY3VtZW50cyBpbiBvbmUgcmVxdWVzdC5cbiAgICogU2hvdWxkIGJlIHVzZWQgc3BhcmluZ2x5IC0gcGVyZm9ybWluZyBhIHF1ZXJ5IGlzIHVzdWFsbHkgYSBiZXR0ZXIgb3B0aW9uLlxuICAgKiBUaGUgb3JkZXIvcG9zaXRpb24gb2YgZG9jdW1lbnRzIGlzIHByZXNlcnZlZCBiYXNlZCBvbiB0aGUgb3JpZ2luYWwgYXJyYXkgb2YgSURzLlxuICAgKiBJZiBhbnkgb2YgdGhlIGRvY3VtZW50cyBhcmUgbWlzc2luZywgdGhleSB3aWxsIGJlIHJlcGxhY2VkIGJ5IGEgYG51bGxgIGVudHJ5IGluIHRoZSByZXR1cm5lZCBhcnJheVxuICAgKlxuICAgKiBAcGFyYW0gaWRzIC0gRG9jdW1lbnQgSURzIHRvIGZldGNoXG4gICAqIEBwYXJhbSBvcHRpb25zIC0gUmVxdWVzdCBvcHRpb25zXG4gICAqL1xuICBnZXREb2N1bWVudHMoaWRzLCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oX2dldERvY3VtZW50cyh0aGlzLCB0aGlzLiNodHRwUmVxdWVzdCwgaWRzLCBvcHRpb25zKSk7XG4gIH1cbiAgY3JlYXRlKGRvY3VtZW50LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oXG4gICAgICBfY3JlYXRlKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBkb2N1bWVudCwgXCJjcmVhdGVcIiwgb3B0aW9ucylcbiAgICApO1xuICB9XG4gIGNyZWF0ZUlmTm90RXhpc3RzKGRvY3VtZW50LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oXG4gICAgICBfY3JlYXRlSWZOb3RFeGlzdHModGhpcywgdGhpcy4jaHR0cFJlcXVlc3QsIGRvY3VtZW50LCBvcHRpb25zKVxuICAgICk7XG4gIH1cbiAgY3JlYXRlT3JSZXBsYWNlKGRvY3VtZW50LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oXG4gICAgICBfY3JlYXRlT3JSZXBsYWNlKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBkb2N1bWVudCwgb3B0aW9ucylcbiAgICApO1xuICB9XG4gIGRlbGV0ZShzZWxlY3Rpb24sIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbGFzdFZhbHVlRnJvbShfZGVsZXRlKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBzZWxlY3Rpb24sIG9wdGlvbnMpKTtcbiAgfVxuICBtdXRhdGUob3BlcmF0aW9ucywgb3B0aW9ucykge1xuICAgIHJldHVybiBsYXN0VmFsdWVGcm9tKF9tdXRhdGUodGhpcywgdGhpcy4jaHR0cFJlcXVlc3QsIG9wZXJhdGlvbnMsIG9wdGlvbnMpKTtcbiAgfVxuICAvKipcbiAgICogQ3JlYXRlIGEgbmV3IGJ1aWxkYWJsZSBwYXRjaCBvZiBvcGVyYXRpb25zIHRvIHBlcmZvcm1cbiAgICpcbiAgICogQHBhcmFtIHNlbGVjdGlvbiAtIERvY3VtZW50IElELCBhbiBhcnJheSBvZiBkb2N1bWVudCBJRHMsIG9yIGFuIG9iamVjdCB3aXRoIGBxdWVyeWAgYW5kIG9wdGlvbmFsIGBwYXJhbXNgLCBkZWZpbmluZyB3aGljaCBkb2N1bWVudChzKSB0byBwYXRjaFxuICAgKiBAcGFyYW0gb3BlcmF0aW9ucyAtIE9wdGlvbmFsIG9iamVjdCBvZiBwYXRjaCBvcGVyYXRpb25zIHRvIGluaXRpYWxpemUgdGhlIHBhdGNoIGluc3RhbmNlIHdpdGhcbiAgICogQHJldHVybnMgUGF0Y2ggaW5zdGFuY2UgLSBjYWxsIGAuY29tbWl0KClgIHRvIHBlcmZvcm0gdGhlIG9wZXJhdGlvbnMgZGVmaW5lZFxuICAgKi9cbiAgcGF0Y2goZG9jdW1lbnRJZCwgb3BlcmF0aW9ucykge1xuICAgIHJldHVybiBuZXcgUGF0Y2goZG9jdW1lbnRJZCwgb3BlcmF0aW9ucywgdGhpcyk7XG4gIH1cbiAgLyoqXG4gICAqIENyZWF0ZSBhIG5ldyB0cmFuc2FjdGlvbiBvZiBtdXRhdGlvbnNcbiAgICpcbiAgICogQHBhcmFtIG9wZXJhdGlvbnMgLSBPcHRpb25hbCBhcnJheSBvZiBtdXRhdGlvbiBvcGVyYXRpb25zIHRvIGluaXRpYWxpemUgdGhlIHRyYW5zYWN0aW9uIGluc3RhbmNlIHdpdGhcbiAgICovXG4gIHRyYW5zYWN0aW9uKG9wZXJhdGlvbnMpIHtcbiAgICByZXR1cm4gbmV3IFRyYW5zYWN0aW9uKG9wZXJhdGlvbnMsIHRoaXMpO1xuICB9XG4gIC8qKlxuICAgKiBQZXJmb3JtIGFjdGlvbiBvcGVyYXRpb25zIGFnYWluc3QgdGhlIGNvbmZpZ3VyZWQgZGF0YXNldFxuICAgKiBSZXR1cm5zIGEgcHJvbWlzZSB0aGF0IHJlc29sdmVzIHRvIHRoZSB0cmFuc2FjdGlvbiByZXN1bHRcbiAgICpcbiAgICogQHBhcmFtIG9wZXJhdGlvbnMgLSBBY3Rpb24gb3BlcmF0aW9uKHMpIHRvIGV4ZWN1dGVcbiAgICogQHBhcmFtIG9wdGlvbnMgLSBBY3Rpb24gb3B0aW9uc1xuICAgKi9cbiAgYWN0aW9uKG9wZXJhdGlvbnMsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gbGFzdFZhbHVlRnJvbShfYWN0aW9uKHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBvcGVyYXRpb25zLCBvcHRpb25zKSk7XG4gIH1cbiAgLyoqXG4gICAqIFBlcmZvcm0gYSByZXF1ZXN0IGFnYWluc3QgdGhlIFNhbml0eSBBUElcbiAgICogTk9URTogT25seSB1c2UgdGhpcyBmb3IgU2FuaXR5IEFQSSBlbmRwb2ludHMsIG5vdCBmb3IgeW91ciBvd24gQVBJcyFcbiAgICpcbiAgICogQHBhcmFtIG9wdGlvbnMgLSBSZXF1ZXN0IG9wdGlvbnNcbiAgICogQHJldHVybnMgUHJvbWlzZSByZXNvbHZpbmcgdG8gdGhlIHJlc3BvbnNlIGJvZHlcbiAgICovXG4gIHJlcXVlc3Qob3B0aW9ucykge1xuICAgIHJldHVybiBsYXN0VmFsdWVGcm9tKF9yZXF1ZXN0KHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBvcHRpb25zKSk7XG4gIH1cbiAgLyoqXG4gICAqIFBlcmZvcm0gYW4gSFRUUCByZXF1ZXN0IGEgYC9kYXRhYCBzdWItZW5kcG9pbnRcbiAgICogTk9URTogQ29uc2lkZXJlZCBpbnRlcm5hbCwgdGh1cyBtYXJrZWQgYXMgZGVwcmVjYXRlZC4gVXNlIGByZXF1ZXN0YCBpbnN0ZWFkLlxuICAgKlxuICAgKiBAZGVwcmVjYXRlZCAtIFVzZSBgcmVxdWVzdCgpYCBvciB5b3VyIG93biBIVFRQIGxpYnJhcnkgaW5zdGVhZFxuICAgKiBAcGFyYW0gZW5kcG9pbnQgLSBFbmRwb2ludCB0byBoaXQgKG11dGF0ZSwgcXVlcnkgZXRjKVxuICAgKiBAcGFyYW0gYm9keSAtIFJlcXVlc3QgYm9keVxuICAgKiBAcGFyYW0gb3B0aW9ucyAtIFJlcXVlc3Qgb3B0aW9uc1xuICAgKiBAaW50ZXJuYWxcbiAgICovXG4gIGRhdGFSZXF1ZXN0KGVuZHBvaW50LCBib2R5LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIGxhc3RWYWx1ZUZyb20oX2RhdGFSZXF1ZXN0KHRoaXMsIHRoaXMuI2h0dHBSZXF1ZXN0LCBlbmRwb2ludCwgYm9keSwgb3B0aW9ucykpO1xuICB9XG4gIC8qKlxuICAgKiBHZXQgYSBTYW5pdHkgQVBJIFVSTCBmb3IgdGhlIFVSSSBwcm92aWRlZFxuICAgKlxuICAgKiBAcGFyYW0gdXJpIC0gVVJJL3BhdGggdG8gYnVpbGQgVVJMIGZvclxuICAgKiBAcGFyYW0gY2FuVXNlQ2RuIC0gV2hldGhlciBvciBub3QgdG8gYWxsb3cgdXNpbmcgdGhlIEFQSSBDRE4gZm9yIHRoaXMgcm91dGVcbiAgICovXG4gIGdldFVybCh1cmksIGNhblVzZUNkbikge1xuICAgIHJldHVybiBfZ2V0VXJsKHRoaXMsIHVyaSwgY2FuVXNlQ2RuKTtcbiAgfVxuICAvKipcbiAgICogR2V0IGEgU2FuaXR5IEFQSSBVUkwgZm9yIHRoZSBkYXRhIG9wZXJhdGlvbiBhbmQgcGF0aCBwcm92aWRlZFxuICAgKlxuICAgKiBAcGFyYW0gb3BlcmF0aW9uIC0gRGF0YSBvcGVyYXRpb24gKGVnIGBxdWVyeWAsIGBtdXRhdGVgLCBgbGlzdGVuYCBvciBzaW1pbGFyKVxuICAgKiBAcGFyYW0gcGF0aCAtIFBhdGggdG8gYXBwZW5kIGFmdGVyIHRoZSBvcGVyYXRpb25cbiAgICovXG4gIGdldERhdGFVcmwob3BlcmF0aW9uLCBwYXRoKSB7XG4gICAgcmV0dXJuIF9nZXREYXRhVXJsKHRoaXMsIG9wZXJhdGlvbiwgcGF0aCk7XG4gIH1cbn1cbmZ1bmN0aW9uIGRlZmluZUNyZWF0ZUNsaWVudEV4cG9ydHMoZW52TWlkZGxld2FyZSwgQ2xhc3NDb25zdHJ1Y3Rvcikge1xuICByZXR1cm4geyByZXF1ZXN0ZXI6IGRlZmluZUh0dHBSZXF1ZXN0KGVudk1pZGRsZXdhcmUpLCBjcmVhdGVDbGllbnQ6IChjb25maWcpID0+IHtcbiAgICBjb25zdCBjbGllbnRSZXF1ZXN0ZXIgPSBkZWZpbmVIdHRwUmVxdWVzdChlbnZNaWRkbGV3YXJlKTtcbiAgICByZXR1cm4gbmV3IENsYXNzQ29uc3RydWN0b3IoXG4gICAgICAob3B0aW9ucywgcmVxdWVzdGVyMikgPT4gKHJlcXVlc3RlcjIgfHwgY2xpZW50UmVxdWVzdGVyKSh7XG4gICAgICAgIG1heFJlZGlyZWN0czogMCxcbiAgICAgICAgbWF4UmV0cmllczogY29uZmlnLm1heFJldHJpZXMsXG4gICAgICAgIHJldHJ5RGVsYXk6IGNvbmZpZy5yZXRyeURlbGF5LFxuICAgICAgICAuLi5vcHRpb25zXG4gICAgICB9KSxcbiAgICAgIGNvbmZpZ1xuICAgICk7XG4gIH0gfTtcbn1cbmZ1bmN0aW9uIGRlZmluZURlcHJlY2F0ZWRDcmVhdGVDbGllbnQoY3JlYXRlQ2xpZW50Mikge1xuICByZXR1cm4gZnVuY3Rpb24oY29uZmlnKSB7XG4gICAgcmV0dXJuIHByaW50Tm9EZWZhdWx0RXhwb3J0KCksIGNyZWF0ZUNsaWVudDIoY29uZmlnKTtcbiAgfTtcbn1cbnZhciBuYW1lID0gXCJAc2FuaXR5L2NsaWVudFwiLCB2ZXJzaW9uID0gXCI3LjEuMFwiO1xuY29uc3QgbWlkZGxld2FyZSA9IFtcbiAgZGVidWcoeyB2ZXJib3NlOiAhMCwgbmFtZXNwYWNlOiBcInNhbml0eTpjbGllbnRcIiB9KSxcbiAgaGVhZGVycyh7IFwiVXNlci1BZ2VudFwiOiBgJHtuYW1lfSAke3ZlcnNpb259YCB9KSxcbiAgLy8gRW5hYmxlIGtlZXAtYWxpdmUsIGFuZCBpbiBhZGRpdGlvbiBsaW1pdCB0aGUgbnVtYmVyIG9mIHNvY2tldHMgdGhhdCBjYW4gYmUgb3BlbmVkLlxuICAvLyBUaGlzIGF2b2lkcyBvcGVuaW5nIHRvbyBtYW55IGNvbm5lY3Rpb25zIHRvIHRoZSBzZXJ2ZXIgaWYgc29tZW9uZSB0cmllcyB0byBleGVjdXRlXG4gIC8vIGEgYnVuY2ggb2YgcmVxdWVzdHMgaW4gcGFyYWxsZWwuIEl0J3MgcmVjb21tZW5kZWQgdG8gaGF2ZSBhIGNvbmN1cnJlbmN5IGxpbWl0XG4gIC8vIGF0IGEgXCJoaWdoZXIgbGltaXRcIiAoaS5lLiB5b3Ugc2hvdWxkbid0IGFjdHVhbGx5IGV4ZWN1dGUgaHVuZHJlZHMgb2YgcmVxdWVzdHMgaW4gcGFyYWxsZWwpLFxuICAvLyBhbmQgdGhpcyBpcyBtYWlubHkgdG8gbWluaW1pemUgdGhlIGltcGFjdCBmb3IgdGhlIG5ldHdvcmsgYW5kIHNlcnZlci5cbiAgLy9cbiAgLy8gV2UncmUgY3VycmVudGx5IG1hdGNoaW5nIHRoZSBzYW1lIGRlZmF1bHRzIGFzIGJyb3dzZXJzOlxuICAvLyBodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL3F1ZXN0aW9ucy8yNjAwMzc1Ni9pcy10aGVyZS1hLWxpbWl0LXByYWN0aWNhbC1vci1vdGhlcndpc2UtdG8tdGhlLW51bWJlci1vZi13ZWItc29ja2V0cy1hLXBhZ2Utb3BcbiAgYWdlbnQoe1xuICAgIGtlZXBBbGl2ZTogITAsXG4gICAgbWF4U29ja2V0czogMzAsXG4gICAgbWF4VG90YWxTb2NrZXRzOiAyNTZcbiAgfSlcbl0sIGV4cCA9IGRlZmluZUNyZWF0ZUNsaWVudEV4cG9ydHMobWlkZGxld2FyZSwgU2FuaXR5Q2xpZW50KSwgcmVxdWVzdGVyID0gZXhwLnJlcXVlc3RlciwgY3JlYXRlQ2xpZW50ID0gZXhwLmNyZWF0ZUNsaWVudCwgZGVwcmVjYXRlZENyZWF0ZUNsaWVudCA9IGRlZmluZURlcHJlY2F0ZWRDcmVhdGVDbGllbnQoY3JlYXRlQ2xpZW50KTtcbmV4cG9ydCB7XG4gIEJhc2VQYXRjaCxcbiAgQmFzZVRyYW5zYWN0aW9uLFxuICBDaGFubmVsRXJyb3IsXG4gIENsaWVudEVycm9yLFxuICBDb25uZWN0aW9uRmFpbGVkRXJyb3IsXG4gIENvcnNPcmlnaW5FcnJvcixcbiAgRGlzY29ubmVjdEVycm9yLFxuICBNZXNzYWdlRXJyb3IsXG4gIE1lc3NhZ2VQYXJzZUVycm9yLFxuICBPYnNlcnZhYmxlUGF0Y2gsXG4gIE9ic2VydmFibGVTYW5pdHlDbGllbnQsXG4gIE9ic2VydmFibGVUcmFuc2FjdGlvbixcbiAgUGF0Y2gsXG4gIFNhbml0eUNsaWVudCxcbiAgU2VydmVyRXJyb3IsXG4gIFRyYW5zYWN0aW9uLFxuICBjb25uZWN0RXZlbnRTb3VyY2UsXG4gIGNyZWF0ZUNsaWVudCxcbiAgZGVwcmVjYXRlZENyZWF0ZUNsaWVudCBhcyBkZWZhdWx0LFxuICByZXF1ZXN0ZXIsXG4gIGFkYXB0ZXIgYXMgdW5zdGFibGVfX2FkYXB0ZXIsXG4gIGVudmlyb25tZW50IGFzIHVuc3RhYmxlX19lbnZpcm9ubWVudCxcbiAgdmFsaWRhdGVBcGlQZXJzcGVjdGl2ZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/client/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/eventsource/node.js":
/*!**************************************************!*\
  !*** ./node_modules/@sanity/eventsource/node.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! eventsource */ \"(ssr)/./node_modules/@sanity/eventsource/node_modules/eventsource/lib/eventsource.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9ldmVudHNvdXJjZS9ub2RlLmpzIiwibWFwcGluZ3MiOiJBQUFBLCtJQUF1QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFJlbnRlck5nXFxyZW50ZXJzbmctYXBwXFxub2RlX21vZHVsZXNcXEBzYW5pdHlcXGV2ZW50c291cmNlXFxub2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnZXZlbnRzb3VyY2UnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/eventsource/node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/eventsource/node_modules/eventsource/lib/eventsource.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/@sanity/eventsource/node_modules/eventsource/lib/eventsource.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var parse = (__webpack_require__(/*! url */ \"url\").parse)\nvar events = __webpack_require__(/*! events */ \"events\")\nvar https = __webpack_require__(/*! https */ \"https\")\nvar http = __webpack_require__(/*! http */ \"http\")\nvar util = __webpack_require__(/*! util */ \"util\")\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/eventsource/node_modules/eventsource/lib/eventsource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/image-url/lib/node/builder.js":
/*!************************************************************!*\
  !*** ./node_modules/@sanity/image-url/lib/node/builder.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ImageUrlBuilder = void 0;\nvar urlForImage_1 = __importStar(__webpack_require__(/*! ./urlForImage */ \"(ssr)/./node_modules/@sanity/image-url/lib/node/urlForImage.js\"));\nvar validFits = ['clip', 'crop', 'fill', 'fillmax', 'max', 'scale', 'min'];\nvar validCrops = ['top', 'bottom', 'left', 'right', 'center', 'focalpoint', 'entropy'];\nvar validAutoModes = ['format'];\nfunction isSanityModernClientLike(client) {\n    return client && 'config' in client ? typeof client.config === 'function' : false;\n}\nfunction isSanityClientLike(client) {\n    return client && 'clientConfig' in client ? typeof client.clientConfig === 'object' : false;\n}\nfunction rewriteSpecName(key) {\n    var specs = urlForImage_1.SPEC_NAME_TO_URL_NAME_MAPPINGS;\n    for (var _i = 0, specs_1 = specs; _i < specs_1.length; _i++) {\n        var entry = specs_1[_i];\n        var specName = entry[0], param = entry[1];\n        if (key === specName || key === param) {\n            return specName;\n        }\n    }\n    return key;\n}\nfunction urlBuilder(options) {\n    // Did we get a modernish client?\n    if (isSanityModernClientLike(options)) {\n        // Inherit config from client\n        var _a = options.config(), apiUrl = _a.apiHost, projectId = _a.projectId, dataset = _a.dataset;\n        var apiHost = apiUrl || 'https://api.sanity.io';\n        return new ImageUrlBuilder(null, {\n            baseUrl: apiHost.replace(/^https:\\/\\/api\\./, 'https://cdn.'),\n            projectId: projectId,\n            dataset: dataset,\n        });\n    }\n    // Did we get a SanityClient?\n    if (isSanityClientLike(options)) {\n        // Inherit config from client\n        var _b = options.clientConfig, apiUrl = _b.apiHost, projectId = _b.projectId, dataset = _b.dataset;\n        var apiHost = apiUrl || 'https://api.sanity.io';\n        return new ImageUrlBuilder(null, {\n            baseUrl: apiHost.replace(/^https:\\/\\/api\\./, 'https://cdn.'),\n            projectId: projectId,\n            dataset: dataset,\n        });\n    }\n    // Or just accept the options as given\n    return new ImageUrlBuilder(null, options || {});\n}\nexports[\"default\"] = urlBuilder;\nvar ImageUrlBuilder = /** @class */ (function () {\n    function ImageUrlBuilder(parent, options) {\n        this.options = parent\n            ? __assign(__assign({}, (parent.options || {})), (options || {})) : __assign({}, (options || {})); // Copy options\n    }\n    ImageUrlBuilder.prototype.withOptions = function (options) {\n        var baseUrl = options.baseUrl || this.options.baseUrl;\n        var newOptions = { baseUrl: baseUrl };\n        for (var key in options) {\n            if (options.hasOwnProperty(key)) {\n                var specKey = rewriteSpecName(key);\n                newOptions[specKey] = options[key];\n            }\n        }\n        return new ImageUrlBuilder(this, __assign({ baseUrl: baseUrl }, newOptions));\n    };\n    // The image to be represented. Accepts a Sanity 'image'-document, 'asset'-document or\n    // _id of asset. To get the benefit of automatic hot-spot/crop integration with the content\n    // studio, the 'image'-document must be provided.\n    ImageUrlBuilder.prototype.image = function (source) {\n        return this.withOptions({ source: source });\n    };\n    // Specify the dataset\n    ImageUrlBuilder.prototype.dataset = function (dataset) {\n        return this.withOptions({ dataset: dataset });\n    };\n    // Specify the projectId\n    ImageUrlBuilder.prototype.projectId = function (projectId) {\n        return this.withOptions({ projectId: projectId });\n    };\n    // Specify background color\n    ImageUrlBuilder.prototype.bg = function (bg) {\n        return this.withOptions({ bg: bg });\n    };\n    // Set DPR scaling factor\n    ImageUrlBuilder.prototype.dpr = function (dpr) {\n        // A DPR of 1 is the default - so only include it if we have a different value\n        return this.withOptions(dpr && dpr !== 1 ? { dpr: dpr } : {});\n    };\n    // Specify the width of the image in pixels\n    ImageUrlBuilder.prototype.width = function (width) {\n        return this.withOptions({ width: width });\n    };\n    // Specify the height of the image in pixels\n    ImageUrlBuilder.prototype.height = function (height) {\n        return this.withOptions({ height: height });\n    };\n    // Specify focal point in fraction of image dimensions. Each component 0.0-1.0\n    ImageUrlBuilder.prototype.focalPoint = function (x, y) {\n        return this.withOptions({ focalPoint: { x: x, y: y } });\n    };\n    ImageUrlBuilder.prototype.maxWidth = function (maxWidth) {\n        return this.withOptions({ maxWidth: maxWidth });\n    };\n    ImageUrlBuilder.prototype.minWidth = function (minWidth) {\n        return this.withOptions({ minWidth: minWidth });\n    };\n    ImageUrlBuilder.prototype.maxHeight = function (maxHeight) {\n        return this.withOptions({ maxHeight: maxHeight });\n    };\n    ImageUrlBuilder.prototype.minHeight = function (minHeight) {\n        return this.withOptions({ minHeight: minHeight });\n    };\n    // Specify width and height in pixels\n    ImageUrlBuilder.prototype.size = function (width, height) {\n        return this.withOptions({ width: width, height: height });\n    };\n    // Specify blur between 0 and 100\n    ImageUrlBuilder.prototype.blur = function (blur) {\n        return this.withOptions({ blur: blur });\n    };\n    ImageUrlBuilder.prototype.sharpen = function (sharpen) {\n        return this.withOptions({ sharpen: sharpen });\n    };\n    // Specify the desired rectangle of the image\n    ImageUrlBuilder.prototype.rect = function (left, top, width, height) {\n        return this.withOptions({ rect: { left: left, top: top, width: width, height: height } });\n    };\n    // Specify the image format of the image. 'jpg', 'pjpg', 'png', 'webp'\n    ImageUrlBuilder.prototype.format = function (format) {\n        return this.withOptions({ format: format });\n    };\n    ImageUrlBuilder.prototype.invert = function (invert) {\n        return this.withOptions({ invert: invert });\n    };\n    // Rotation in degrees 0, 90, 180, 270\n    ImageUrlBuilder.prototype.orientation = function (orientation) {\n        return this.withOptions({ orientation: orientation });\n    };\n    // Compression quality 0-100\n    ImageUrlBuilder.prototype.quality = function (quality) {\n        return this.withOptions({ quality: quality });\n    };\n    // Make it a download link. Parameter is default filename.\n    ImageUrlBuilder.prototype.forceDownload = function (download) {\n        return this.withOptions({ download: download });\n    };\n    // Flip image horizontally\n    ImageUrlBuilder.prototype.flipHorizontal = function () {\n        return this.withOptions({ flipHorizontal: true });\n    };\n    // Flip image vertically\n    ImageUrlBuilder.prototype.flipVertical = function () {\n        return this.withOptions({ flipVertical: true });\n    };\n    // Ignore crop/hotspot from image record, even when present\n    ImageUrlBuilder.prototype.ignoreImageParams = function () {\n        return this.withOptions({ ignoreImageParams: true });\n    };\n    ImageUrlBuilder.prototype.fit = function (value) {\n        if (validFits.indexOf(value) === -1) {\n            throw new Error(\"Invalid fit mode \\\"\".concat(value, \"\\\"\"));\n        }\n        return this.withOptions({ fit: value });\n    };\n    ImageUrlBuilder.prototype.crop = function (value) {\n        if (validCrops.indexOf(value) === -1) {\n            throw new Error(\"Invalid crop mode \\\"\".concat(value, \"\\\"\"));\n        }\n        return this.withOptions({ crop: value });\n    };\n    // Saturation\n    ImageUrlBuilder.prototype.saturation = function (saturation) {\n        return this.withOptions({ saturation: saturation });\n    };\n    ImageUrlBuilder.prototype.auto = function (value) {\n        if (validAutoModes.indexOf(value) === -1) {\n            throw new Error(\"Invalid auto mode \\\"\".concat(value, \"\\\"\"));\n        }\n        return this.withOptions({ auto: value });\n    };\n    // Specify the number of pixels to pad the image\n    ImageUrlBuilder.prototype.pad = function (pad) {\n        return this.withOptions({ pad: pad });\n    };\n    // Vanity URL for more SEO friendly URLs\n    ImageUrlBuilder.prototype.vanityName = function (value) {\n        return this.withOptions({ vanityName: value });\n    };\n    ImageUrlBuilder.prototype.frame = function (frame) {\n        if (frame !== 1) {\n            throw new Error(\"Invalid frame value \\\"\".concat(frame, \"\\\"\"));\n        }\n        return this.withOptions({ frame: frame });\n    };\n    // Gets the url based on the submitted parameters\n    ImageUrlBuilder.prototype.url = function () {\n        return (0, urlForImage_1.default)(this.options);\n    };\n    // Alias for url()\n    ImageUrlBuilder.prototype.toString = function () {\n        return this.url();\n    };\n    return ImageUrlBuilder;\n}());\nexports.ImageUrlBuilder = ImageUrlBuilder;\n//# sourceMappingURL=builder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/image-url/lib/node/builder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/image-url/lib/node/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@sanity/image-url/lib/node/index.js ***!
  \**********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar builder_1 = __importDefault(__webpack_require__(/*! ./builder */ \"(ssr)/./node_modules/@sanity/image-url/lib/node/builder.js\"));\nmodule.exports = builder_1.default;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9pbWFnZS11cmwvbGliL25vZGUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLGdDQUFnQyxtQkFBTyxDQUFDLDZFQUFXO0FBQ25EO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxSZW50ZXJOZ1xccmVudGVyc25nLWFwcFxcbm9kZV9tb2R1bGVzXFxAc2FuaXR5XFxpbWFnZS11cmxcXGxpYlxcbm9kZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG52YXIgYnVpbGRlcl8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCIuL2J1aWxkZXJcIikpO1xubW9kdWxlLmV4cG9ydHMgPSBidWlsZGVyXzEuZGVmYXVsdDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/image-url/lib/node/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/image-url/lib/node/parseAssetId.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@sanity/image-url/lib/node/parseAssetId.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar example = 'image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg';\nfunction parseAssetId(ref) {\n    var _a = ref.split('-'), id = _a[1], dimensionString = _a[2], format = _a[3];\n    if (!id || !dimensionString || !format) {\n        throw new Error(\"Malformed asset _ref '\".concat(ref, \"'. Expected an id like \\\"\").concat(example, \"\\\".\"));\n    }\n    var _b = dimensionString.split('x'), imgWidthStr = _b[0], imgHeightStr = _b[1];\n    var width = +imgWidthStr;\n    var height = +imgHeightStr;\n    var isValidAssetId = isFinite(width) && isFinite(height);\n    if (!isValidAssetId) {\n        throw new Error(\"Malformed asset _ref '\".concat(ref, \"'. Expected an id like \\\"\").concat(example, \"\\\".\"));\n    }\n    return { id: id, width: width, height: height, format: format };\n}\nexports[\"default\"] = parseAssetId;\n//# sourceMappingURL=parseAssetId.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9pbWFnZS11cmwvbGliL25vZGUvcGFyc2VBc3NldElkLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFJlbnRlck5nXFxyZW50ZXJzbmctYXBwXFxub2RlX21vZHVsZXNcXEBzYW5pdHlcXGltYWdlLXVybFxcbGliXFxub2RlXFxwYXJzZUFzc2V0SWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG52YXIgZXhhbXBsZSA9ICdpbWFnZS1UYjlFdzhDWEl3YVk2UjFrak12STB1UlItMjAwMHgzMDAwLWpwZyc7XG5mdW5jdGlvbiBwYXJzZUFzc2V0SWQocmVmKSB7XG4gICAgdmFyIF9hID0gcmVmLnNwbGl0KCctJyksIGlkID0gX2FbMV0sIGRpbWVuc2lvblN0cmluZyA9IF9hWzJdLCBmb3JtYXQgPSBfYVszXTtcbiAgICBpZiAoIWlkIHx8ICFkaW1lbnNpb25TdHJpbmcgfHwgIWZvcm1hdCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJNYWxmb3JtZWQgYXNzZXQgX3JlZiAnXCIuY29uY2F0KHJlZiwgXCInLiBFeHBlY3RlZCBhbiBpZCBsaWtlIFxcXCJcIikuY29uY2F0KGV4YW1wbGUsIFwiXFxcIi5cIikpO1xuICAgIH1cbiAgICB2YXIgX2IgPSBkaW1lbnNpb25TdHJpbmcuc3BsaXQoJ3gnKSwgaW1nV2lkdGhTdHIgPSBfYlswXSwgaW1nSGVpZ2h0U3RyID0gX2JbMV07XG4gICAgdmFyIHdpZHRoID0gK2ltZ1dpZHRoU3RyO1xuICAgIHZhciBoZWlnaHQgPSAraW1nSGVpZ2h0U3RyO1xuICAgIHZhciBpc1ZhbGlkQXNzZXRJZCA9IGlzRmluaXRlKHdpZHRoKSAmJiBpc0Zpbml0ZShoZWlnaHQpO1xuICAgIGlmICghaXNWYWxpZEFzc2V0SWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTWFsZm9ybWVkIGFzc2V0IF9yZWYgJ1wiLmNvbmNhdChyZWYsIFwiJy4gRXhwZWN0ZWQgYW4gaWQgbGlrZSBcXFwiXCIpLmNvbmNhdChleGFtcGxlLCBcIlxcXCIuXCIpKTtcbiAgICB9XG4gICAgcmV0dXJuIHsgaWQ6IGlkLCB3aWR0aDogd2lkdGgsIGhlaWdodDogaGVpZ2h0LCBmb3JtYXQ6IGZvcm1hdCB9O1xufVxuZXhwb3J0cy5kZWZhdWx0ID0gcGFyc2VBc3NldElkO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFyc2VBc3NldElkLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/image-url/lib/node/parseAssetId.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/image-url/lib/node/parseSource.js":
/*!****************************************************************!*\
  !*** ./node_modules/@sanity/image-url/lib/node/parseSource.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports) {

"use strict";
eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar isRef = function (src) {\n    var source = src;\n    return source ? typeof source._ref === 'string' : false;\n};\nvar isAsset = function (src) {\n    var source = src;\n    return source ? typeof source._id === 'string' : false;\n};\nvar isAssetStub = function (src) {\n    var source = src;\n    return source && source.asset ? typeof source.asset.url === 'string' : false;\n};\n// Convert an asset-id, asset or image to an image record suitable for processing\n// eslint-disable-next-line complexity\nfunction parseSource(source) {\n    if (!source) {\n        return null;\n    }\n    var image;\n    if (typeof source === 'string' && isUrl(source)) {\n        // Someone passed an existing image url?\n        image = {\n            asset: { _ref: urlToId(source) },\n        };\n    }\n    else if (typeof source === 'string') {\n        // Just an asset id\n        image = {\n            asset: { _ref: source },\n        };\n    }\n    else if (isRef(source)) {\n        // We just got passed an asset directly\n        image = {\n            asset: source,\n        };\n    }\n    else if (isAsset(source)) {\n        // If we were passed an image asset document\n        image = {\n            asset: {\n                _ref: source._id || '',\n            },\n        };\n    }\n    else if (isAssetStub(source)) {\n        // If we were passed a partial asset (`url`, but no `_id`)\n        image = {\n            asset: {\n                _ref: urlToId(source.asset.url),\n            },\n        };\n    }\n    else if (typeof source.asset === 'object') {\n        // Probably an actual image with materialized asset\n        image = __assign({}, source);\n    }\n    else {\n        // We got something that does not look like an image, or it is an image\n        // that currently isn't sporting an asset.\n        return null;\n    }\n    var img = source;\n    if (img.crop) {\n        image.crop = img.crop;\n    }\n    if (img.hotspot) {\n        image.hotspot = img.hotspot;\n    }\n    return applyDefaults(image);\n}\nexports[\"default\"] = parseSource;\nfunction isUrl(url) {\n    return /^https?:\\/\\//.test(\"\".concat(url));\n}\nfunction urlToId(url) {\n    var parts = url.split('/').slice(-1);\n    return \"image-\".concat(parts[0]).replace(/\\.([a-z]+)$/, '-$1');\n}\n// Mock crop and hotspot if image lacks it\nfunction applyDefaults(image) {\n    if (image.crop && image.hotspot) {\n        return image;\n    }\n    // We need to pad in default values for crop or hotspot\n    var result = __assign({}, image);\n    if (!result.crop) {\n        result.crop = {\n            left: 0,\n            top: 0,\n            bottom: 0,\n            right: 0,\n        };\n    }\n    if (!result.hotspot) {\n        result.hotspot = {\n            x: 0.5,\n            y: 0.5,\n            height: 1.0,\n            width: 1.0,\n        };\n    }\n    return result;\n}\n//# sourceMappingURL=parseSource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/image-url/lib/node/parseSource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@sanity/image-url/lib/node/urlForImage.js":
/*!****************************************************************!*\
  !*** ./node_modules/@sanity/image-url/lib/node/urlForImage.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseSource = exports.SPEC_NAME_TO_URL_NAME_MAPPINGS = void 0;\nvar parseAssetId_1 = __importDefault(__webpack_require__(/*! ./parseAssetId */ \"(ssr)/./node_modules/@sanity/image-url/lib/node/parseAssetId.js\"));\nvar parseSource_1 = __importDefault(__webpack_require__(/*! ./parseSource */ \"(ssr)/./node_modules/@sanity/image-url/lib/node/parseSource.js\"));\nexports.parseSource = parseSource_1.default;\nexports.SPEC_NAME_TO_URL_NAME_MAPPINGS = [\n    ['width', 'w'],\n    ['height', 'h'],\n    ['format', 'fm'],\n    ['download', 'dl'],\n    ['blur', 'blur'],\n    ['sharpen', 'sharp'],\n    ['invert', 'invert'],\n    ['orientation', 'or'],\n    ['minHeight', 'min-h'],\n    ['maxHeight', 'max-h'],\n    ['minWidth', 'min-w'],\n    ['maxWidth', 'max-w'],\n    ['quality', 'q'],\n    ['fit', 'fit'],\n    ['crop', 'crop'],\n    ['saturation', 'sat'],\n    ['auto', 'auto'],\n    ['dpr', 'dpr'],\n    ['pad', 'pad'],\n    ['frame', 'frame']\n];\nfunction urlForImage(options) {\n    var spec = __assign({}, (options || {}));\n    var source = spec.source;\n    delete spec.source;\n    var image = (0, parseSource_1.default)(source);\n    if (!image) {\n        throw new Error(\"Unable to resolve image URL from source (\".concat(JSON.stringify(source), \")\"));\n    }\n    var id = image.asset._ref || image.asset._id || '';\n    var asset = (0, parseAssetId_1.default)(id);\n    // Compute crop rect in terms of pixel coordinates in the raw source image\n    var cropLeft = Math.round(image.crop.left * asset.width);\n    var cropTop = Math.round(image.crop.top * asset.height);\n    var crop = {\n        left: cropLeft,\n        top: cropTop,\n        width: Math.round(asset.width - image.crop.right * asset.width - cropLeft),\n        height: Math.round(asset.height - image.crop.bottom * asset.height - cropTop),\n    };\n    // Compute hot spot rect in terms of pixel coordinates\n    var hotSpotVerticalRadius = (image.hotspot.height * asset.height) / 2;\n    var hotSpotHorizontalRadius = (image.hotspot.width * asset.width) / 2;\n    var hotSpotCenterX = image.hotspot.x * asset.width;\n    var hotSpotCenterY = image.hotspot.y * asset.height;\n    var hotspot = {\n        left: hotSpotCenterX - hotSpotHorizontalRadius,\n        top: hotSpotCenterY - hotSpotVerticalRadius,\n        right: hotSpotCenterX + hotSpotHorizontalRadius,\n        bottom: hotSpotCenterY + hotSpotVerticalRadius,\n    };\n    // If irrelevant, or if we are requested to: don't perform crop/fit based on\n    // the crop/hotspot.\n    if (!(spec.rect || spec.focalPoint || spec.ignoreImageParams || spec.crop)) {\n        spec = __assign(__assign({}, spec), fit({ crop: crop, hotspot: hotspot }, spec));\n    }\n    return specToImageUrl(__assign(__assign({}, spec), { asset: asset }));\n}\nexports[\"default\"] = urlForImage;\n// eslint-disable-next-line complexity\nfunction specToImageUrl(spec) {\n    var cdnUrl = (spec.baseUrl || 'https://cdn.sanity.io').replace(/\\/+$/, '');\n    var vanityStub = spec.vanityName ? \"/\".concat(spec.vanityName) : '';\n    var filename = \"\".concat(spec.asset.id, \"-\").concat(spec.asset.width, \"x\").concat(spec.asset.height, \".\").concat(spec.asset.format).concat(vanityStub);\n    var baseUrl = \"\".concat(cdnUrl, \"/images/\").concat(spec.projectId, \"/\").concat(spec.dataset, \"/\").concat(filename);\n    var params = [];\n    if (spec.rect) {\n        // Only bother url with a crop if it actually crops anything\n        var _a = spec.rect, left = _a.left, top_1 = _a.top, width = _a.width, height = _a.height;\n        var isEffectiveCrop = left !== 0 || top_1 !== 0 || height !== spec.asset.height || width !== spec.asset.width;\n        if (isEffectiveCrop) {\n            params.push(\"rect=\".concat(left, \",\").concat(top_1, \",\").concat(width, \",\").concat(height));\n        }\n    }\n    if (spec.bg) {\n        params.push(\"bg=\".concat(spec.bg));\n    }\n    if (spec.focalPoint) {\n        params.push(\"fp-x=\".concat(spec.focalPoint.x));\n        params.push(\"fp-y=\".concat(spec.focalPoint.y));\n    }\n    var flip = [spec.flipHorizontal && 'h', spec.flipVertical && 'v'].filter(Boolean).join('');\n    if (flip) {\n        params.push(\"flip=\".concat(flip));\n    }\n    // Map from spec name to url param name, and allow using the actual param name as an alternative\n    exports.SPEC_NAME_TO_URL_NAME_MAPPINGS.forEach(function (mapping) {\n        var specName = mapping[0], param = mapping[1];\n        if (typeof spec[specName] !== 'undefined') {\n            params.push(\"\".concat(param, \"=\").concat(encodeURIComponent(spec[specName])));\n        }\n        else if (typeof spec[param] !== 'undefined') {\n            params.push(\"\".concat(param, \"=\").concat(encodeURIComponent(spec[param])));\n        }\n    });\n    if (params.length === 0) {\n        return baseUrl;\n    }\n    return \"\".concat(baseUrl, \"?\").concat(params.join('&'));\n}\nfunction fit(source, spec) {\n    var cropRect;\n    var imgWidth = spec.width;\n    var imgHeight = spec.height;\n    // If we are not constraining the aspect ratio, we'll just use the whole crop\n    if (!(imgWidth && imgHeight)) {\n        return { width: imgWidth, height: imgHeight, rect: source.crop };\n    }\n    var crop = source.crop;\n    var hotspot = source.hotspot;\n    // If we are here, that means aspect ratio is locked and fitting will be a bit harder\n    var desiredAspectRatio = imgWidth / imgHeight;\n    var cropAspectRatio = crop.width / crop.height;\n    if (cropAspectRatio > desiredAspectRatio) {\n        // The crop is wider than the desired aspect ratio. That means we are cutting from the sides\n        var height = Math.round(crop.height);\n        var width = Math.round(height * desiredAspectRatio);\n        var top_2 = Math.max(0, Math.round(crop.top));\n        // Center output horizontally over hotspot\n        var hotspotXCenter = Math.round((hotspot.right - hotspot.left) / 2 + hotspot.left);\n        var left = Math.max(0, Math.round(hotspotXCenter - width / 2));\n        // Keep output within crop\n        if (left < crop.left) {\n            left = crop.left;\n        }\n        else if (left + width > crop.left + crop.width) {\n            left = crop.left + crop.width - width;\n        }\n        cropRect = { left: left, top: top_2, width: width, height: height };\n    }\n    else {\n        // The crop is taller than the desired ratio, we are cutting from top and bottom\n        var width = crop.width;\n        var height = Math.round(width / desiredAspectRatio);\n        var left = Math.max(0, Math.round(crop.left));\n        // Center output vertically over hotspot\n        var hotspotYCenter = Math.round((hotspot.bottom - hotspot.top) / 2 + hotspot.top);\n        var top_3 = Math.max(0, Math.round(hotspotYCenter - height / 2));\n        // Keep output rect within crop\n        if (top_3 < crop.top) {\n            top_3 = crop.top;\n        }\n        else if (top_3 + height > crop.top + crop.height) {\n            top_3 = crop.top + crop.height - height;\n        }\n        cropRect = { left: left, top: top_3, width: width, height: height };\n    }\n    return {\n        width: imgWidth,\n        height: imgHeight,\n        rect: cropRect,\n    };\n}\n//# sourceMappingURL=urlForImage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@sanity/image-url/lib/node/urlForImage.js\n");

/***/ })

};
;