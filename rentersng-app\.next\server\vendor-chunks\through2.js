/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/through2";
exports.ids = ["vendor-chunks/through2"];
exports.modules = {

/***/ "(ssr)/./node_modules/through2/through2.js":
/*!*******************************************!*\
  !*** ./node_modules/through2/through2.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Transform = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Transform)\n  , inherits  = (__webpack_require__(/*! util */ \"util\").inherits)\n  , xtend     = __webpack_require__(/*! xtend */ \"(ssr)/./node_modules/xtend/immutable.js\")\n\nfunction DestroyableTransform(opts) {\n  Transform.call(this, opts)\n  this._destroyed = false\n}\n\ninherits(DestroyableTransform, Transform)\n\nDestroyableTransform.prototype.destroy = function(err) {\n  if (this._destroyed) return\n  this._destroyed = true\n  \n  var self = this\n  process.nextTick(function() {\n    if (err)\n      self.emit('error', err)\n    self.emit('close')\n  })\n}\n\n// a noop _transform function\nfunction noop (chunk, enc, callback) {\n  callback(null, chunk)\n}\n\n\n// create a new export function, used by both the main export and\n// the .ctor export, contains common logic for dealing with arguments\nfunction through2 (construct) {\n  return function (options, transform, flush) {\n    if (typeof options == 'function') {\n      flush     = transform\n      transform = options\n      options   = {}\n    }\n\n    if (typeof transform != 'function')\n      transform = noop\n\n    if (typeof flush != 'function')\n      flush = null\n\n    return construct(options, transform, flush)\n  }\n}\n\n\n// main export, just make me a transform stream!\nmodule.exports = through2(function (options, transform, flush) {\n  var t2 = new DestroyableTransform(options)\n\n  t2._transform = transform\n\n  if (flush)\n    t2._flush = flush\n\n  return t2\n})\n\n\n// make me a reusable prototype that I can `new`, or implicitly `new`\n// with a constructor call\nmodule.exports.ctor = through2(function (options, transform, flush) {\n  function Through2 (override) {\n    if (!(this instanceof Through2))\n      return new Through2(override)\n\n    this.options = xtend(options, override)\n\n    DestroyableTransform.call(this, this.options)\n  }\n\n  inherits(Through2, DestroyableTransform)\n\n  Through2.prototype._transform = transform\n\n  if (flush)\n    Through2.prototype._flush = flush\n\n  return Through2\n})\n\n\nmodule.exports.obj = through2(function (options, transform, flush) {\n  var t2 = new DestroyableTransform(xtend({ objectMode: true, highWaterMark: 16 }, options))\n\n  t2._transform = transform\n\n  if (flush)\n    t2._flush = flush\n\n  return t2\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/through2/through2.js\n");

/***/ })

};
;